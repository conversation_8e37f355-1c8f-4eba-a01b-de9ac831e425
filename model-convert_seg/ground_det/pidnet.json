{"MetaData": {"Name": "main_graph", "AcuityVersion": "6", "Platform": "tensorflow", "Org_Platform": "onnx"}, "Layers": {"attach_Conv_/final_layer/conv2/Conv/out0_0": {"name": "attach_Conv_/final_layer/conv2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/final_layer/conv2/Conv/out0_0_acuity_mark_perm_206:out0"], "outputs": ["out0"]}, "Conv_/final_layer/conv2/Conv_1": {"name": "Conv_/final_layer/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 19, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/final_layer/relu_1/Relu_2:out0"], "outputs": ["out0"]}, "Relu_/final_layer/relu_1/Relu_2": {"name": "Relu_/final_layer/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/final_layer/conv1/Conv_3:out0"], "outputs": ["out0"]}, "Conv_/final_layer/conv1/Conv_3": {"name": "Conv_/final_layer/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/final_layer/relu/Relu_4:out0"], "outputs": ["out0"]}, "Relu_/final_layer/relu/Relu_4": {"name": "Relu_/final_layer/relu/Relu", "op": "relu", "inputs": ["@BatchNormalization_/final_layer/bn1/BatchNormalization_5:out0"], "outputs": ["out0"]}, "BatchNormalization_/final_layer/bn1/BatchNormalization_5": {"name": "BatchNormalization_/final_layer/bn1/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@Add_/dfm/Add_2_6_conv_215:out0"], "outputs": ["out0"]}, "Add_/dfm/Add_9": {"name": "Add_/dfm/Add", "op": "add", "inputs": ["@Mul_/dfm/Mul_11:out0", "@Add_/layer5_/layer5_.0/Add_12_conv_217:out0"], "outputs": ["out0"]}, "Add_/dfm/Add_1_10": {"name": "Add_/dfm/Add_1", "op": "add", "inputs": ["@Resize_/Resize_2_13:out0", "@Mul_/dfm/Mul_1_14:out0"], "outputs": ["out0"]}, "Mul_/dfm/Mul_11": {"name": "Mul_/dfm/Mul", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Mul_/dfm/Mul_11_acuity_mark_perm_207:out0", "@Resize_/Resize_2_13:out0"], "outputs": ["out0"]}, "Resize_/Resize_2_13": {"name": "Resize_/Resize_2", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Add_/spp/Add_4_19_conv_219:out0"], "outputs": ["out0"]}, "Mul_/dfm/Mul_1_14": {"name": "Mul_/dfm/Mul_1", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Sigmoid_/dfm/Sigmoid_16:out0", "@Add_/layer5_/layer5_.0/Add_12_conv_217:out0"], "outputs": ["out0"]}, "Sub_/dfm/Sub_15": {"name": "Sub_/dfm/Sub", "op": "subtract", "inputs": ["@Initializer_/pag3/Constant_12_output_0_22:out0", "@Sub_/dfm/Sub_15_acuity_mark_perm_208:out0"], "outputs": ["out0"]}, "Sigmoid_/dfm/Sigmoid_16": {"name": "Sigmoid_/dfm/Sigmoid", "op": "sigmoid", "inputs": ["@Add_/layer5_d/layer5_d.0/Add_23_conv_221:out0"], "outputs": ["out0"]}, "Relu_/spp/shortcut/shortcut.1/Relu_21": {"name": "Relu_/spp/shortcut/shortcut.1/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/shortcut/shortcut.0/BatchNormalization_27:out0"], "outputs": ["out0"]}, "Initializer_/pag3/Constant_12_output_0_22": {"name": "Initializer_/pag3/Constant_12_output_0", "op": "variable", "parameters": {"shape": [1], "is_scalar": true, "type": "float32"}, "inputs": [], "outputs": ["out0"]}, "Relu_/layer5_/layer5_.0/relu_1/Relu_24": {"name": "Relu_/layer5_/layer5_.0/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/layer5_/layer5_.0/conv2/Conv_30:out0"], "outputs": ["out0"]}, "Relu_/relu_6/Relu_25": {"name": "Relu_/relu_6/Relu", "op": "relu", "inputs": ["@Add_/pag4/Add_31:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/shortcut/shortcut.0/BatchNormalization_27": {"name": "BatchNormalization_/spp/shortcut/shortcut.0/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "Conv_/layer5_/layer5_.0/conv2/Conv_30": {"name": "Conv_/layer5_/layer5_.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer5_/layer5_.0/relu/Relu_36:out0"], "outputs": ["out0"]}, "Add_/pag4/Add_31": {"name": "Add_/pag4/Add", "op": "add", "inputs": ["@Mul_/pag4/Mul_1_37:out0", "@Mul_/pag4/Mul_2_38:out0"], "outputs": ["out0"]}, "Relu_/spp/compression/compression.1/Relu_32": {"name": "Relu_/spp/compression/compression.1/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/compression/compression.0/BatchNormalization_39:out0"], "outputs": ["out0"]}, "Add_/layer5/layer5.1/Add_33": {"name": "Add_/layer5/layer5.1/Add", "op": "add", "inputs": ["@Conv_/layer5/layer5.1/conv3/Conv_40:out0", "@Add_/layer5/layer5.0/Add_41:out0"], "outputs": ["out0"]}, "Relu_/layer5_d/layer5_d.0/relu_1/Relu_34": {"name": "Relu_/layer5_d/layer5_d.0/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/layer5_d/layer5_d.0/conv2/Conv_42:out0"], "outputs": ["out0"]}, "Relu_/relu_7/Relu_35": {"name": "Relu_/relu_7/Relu", "op": "relu", "inputs": ["@Add_/Add_1_43:out0"], "outputs": ["out0"]}, "Relu_/layer5_/layer5_.0/relu/Relu_36": {"name": "Relu_/layer5_/layer5_.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer5_/layer5_.0/conv1/Conv_44:out0"], "outputs": ["out0"]}, "Mul_/pag4/Mul_1_37": {"name": "Mul_/pag4/Mul_1", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Mul_/pag4/Mul_1_37_acuity_mark_perm_209:out0", "@Add_/layer4_/layer4_.1/Add_46:out0"], "outputs": ["out0"]}, "Mul_/pag4/Mul_2_38": {"name": "Mul_/pag4/Mul_2", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Sigmoid_/pag4/Sigmoid_47:out0", "@Resize_/pag4/Resize_1_48:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/compression/compression.0/BatchNormalization_39": {"name": "BatchNormalization_/spp/compression/compression.0/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@Concat_/spp/Concat_5_49:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.1/conv3/Conv_40": {"name": "Conv_/layer5/layer5.1/conv3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/layer5/layer5.1/relu_1/Relu_50:out0"], "outputs": ["out0"]}, "Add_/layer5/layer5.0/Add_41": {"name": "Add_/layer5/layer5.0/Add", "op": "add", "inputs": ["@Conv_/layer5/layer5.0/conv3/Conv_51:out0", "@Conv_/layer5/layer5.0/downsample/downsample.0/Conv_52:out0"], "outputs": ["out0"]}, "Conv_/layer5_d/layer5_d.0/conv2/Conv_42": {"name": "Conv_/layer5_d/layer5_d.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer5_d/layer5_d.0/relu/Relu_53:out0"], "outputs": ["out0"]}, "Add_/Add_1_43": {"name": "Add_/Add_1", "op": "add", "inputs": ["@Add_/layer4_d/layer4_d.0/Add_54_conv_223:out0", "@Resize_/Resize_1_55:out0"], "outputs": ["out0"]}, "Conv_/layer5_/layer5_.0/conv1/Conv_44": {"name": "Conv_/layer5_/layer5_.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_6/Relu_25:out0"], "outputs": ["out0"]}, "Sub_/pag4/Sub_45": {"name": "Sub_/pag4/Sub", "op": "subtract", "inputs": ["@Initializer_/pag3/Constant_12_output_0_22:out0", "@Sub_/pag4/Sub_45_acuity_mark_perm_210:out0"], "outputs": ["out0"]}, "Add_/layer4_/layer4_.1/Add_46": {"name": "Add_/layer4_/layer4_.1/Add", "op": "add", "inputs": ["@Conv_/layer4_/layer4_.1/conv2/Conv_59:out0", "@Relu_/layer4_/layer4_.0/relu_1/Relu_60:out0"], "outputs": ["out0"]}, "Sigmoid_/pag4/Sigmoid_47": {"name": "Sigmoid_/pag4/Sigmoid", "op": "sigmoid", "inputs": ["@ReduceSum_/pag4/ReduceSum_61:out0"], "outputs": ["out0"]}, "Resize_/pag4/Resize_1_48": {"name": "Resize_/pag4/Resize_1", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/compression4/compression4.0/Conv_62:out0"], "outputs": ["out0"]}, "Concat_/spp/Concat_5_49": {"name": "Concat_/spp/Concat_5", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Conv_/spp/scale0/scale0.2/Conv_63:out0", "@Conv_/spp/scale_process/scale_process.2/Conv_64:out0"], "outputs": ["out0"]}, "Relu_/layer5/layer5.1/relu_1/Relu_50": {"name": "Relu_/layer5/layer5.1/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/layer5/layer5.1/conv2/Conv_65:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.0/conv3/Conv_51": {"name": "Conv_/layer5/layer5.0/conv3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/layer5/layer5.0/relu_1/Relu_66:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.0/downsample/downsample.0/Conv_52": {"name": "Conv_/layer5/layer5.0/downsample/downsample.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 2, "stride_w": 2, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_3/Relu_58:out0"], "outputs": ["out0"]}, "Relu_/layer5_d/layer5_d.0/relu/Relu_53": {"name": "Relu_/layer5_d/layer5_d.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer5_d/layer5_d.0/conv1/Conv_68:out0"], "outputs": ["out0"]}, "Resize_/Resize_1_55": {"name": "Resize_/Resize_1", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/diff4/diff4.0/Conv_56:out0"], "outputs": ["out0"]}, "Conv_/diff4/diff4.0/Conv_56": {"name": "Conv_/diff4/diff4.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_3/Relu_58:out0"], "outputs": ["out0"]}, "Relu_/relu_3/Relu_58": {"name": "Relu_/relu_3/Relu", "op": "relu", "inputs": ["@Add_/layer4/layer4.2/Add_71:out0"], "outputs": ["out0"]}, "Conv_/layer4_/layer4_.1/conv2/Conv_59": {"name": "Conv_/layer4_/layer4_.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4_/layer4_.1/relu/Relu_72:out0"], "outputs": ["out0"]}, "Relu_/layer4_/layer4_.0/relu_1/Relu_60": {"name": "Relu_/layer4_/layer4_.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer4_/layer4_.0/Add_73:out0"], "outputs": ["out0"]}, "ReduceSum_/pag4/ReduceSum_61": {"name": "ReduceSum_/pag4/ReduceSum", "op": "reducesum", "parameters": {"axis_list": [3], "keep_dims": true}, "inputs": ["@Mul_/pag4/Mul_74:out0"], "outputs": ["out0"]}, "Conv_/compression4/compression4.0/Conv_62": {"name": "Conv_/compression4/compression4.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_3/Relu_58:out0"], "outputs": ["out0"]}, "Conv_/spp/scale0/scale0.2/Conv_63": {"name": "Conv_/spp/scale0/scale0.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 96, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/spp/scale0/scale0.1/Relu_76:out0"], "outputs": ["out0"]}, "Conv_/spp/scale_process/scale_process.2/Conv_64": {"name": "Conv_/spp/scale_process/scale_process.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 384, "padding": "VALID", "bias": true, "group_number": 4, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/spp/scale_process/scale_process.1/Relu_77:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.1/conv2/Conv_65": {"name": "Conv_/layer5/layer5.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer5/layer5.1/relu/Relu_78:out0"], "outputs": ["out0"]}, "Relu_/layer5/layer5.0/relu_1/Relu_66": {"name": "Relu_/layer5/layer5.0/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/layer5/layer5.0/conv2/Conv_67:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.0/conv2/Conv_67": {"name": "Conv_/layer5/layer5.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer5/layer5.0/relu/Relu_79:out0"], "outputs": ["out0"]}, "Conv_/layer5_d/layer5_d.0/conv1/Conv_68": {"name": "Conv_/layer5_d/layer5_d.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_7/Relu_35:out0"], "outputs": ["out0"]}, "Add_/layer4/layer4.2/Add_71": {"name": "Add_/layer4/layer4.2/Add", "op": "add", "inputs": ["@Conv_/layer4/layer4.2/conv2/Conv_83:out0", "@Relu_/layer4/layer4.1/relu_1/Relu_84:out0"], "outputs": ["out0"]}, "Relu_/layer4_/layer4_.1/relu/Relu_72": {"name": "Relu_/layer4_/layer4_.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4_/layer4_.1/conv1/Conv_85:out0"], "outputs": ["out0"]}, "Add_/layer4_/layer4_.0/Add_73": {"name": "Add_/layer4_/layer4_.0/Add", "op": "add", "inputs": ["@Conv_/layer4_/layer4_.0/conv2/Conv_86:out0", "@Relu_/relu_4/Relu_87:out0"], "outputs": ["out0"]}, "Mul_/pag4/Mul_74": {"name": "Mul_/pag4/Mul", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Conv_/pag4/f_x/f_x.0/Conv_88:out0", "@Resize_/pag4/Resize_75:out0"], "outputs": ["out0"]}, "Resize_/pag4/Resize_75": {"name": "Resize_/pag4/Resize", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/pag4/f_y/f_y.0/Conv_89:out0"], "outputs": ["out0"]}, "Relu_/spp/scale0/scale0.1/Relu_76": {"name": "Relu_/spp/scale0/scale0.1/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale0/scale0.0/BatchNormalization_90:out0"], "outputs": ["out0"]}, "Relu_/spp/scale_process/scale_process.1/Relu_77": {"name": "Relu_/spp/scale_process/scale_process.1/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale_process/scale_process.0/BatchNormalization_91:out0"], "outputs": ["out0"]}, "Relu_/layer5/layer5.1/relu/Relu_78": {"name": "Relu_/layer5/layer5.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer5/layer5.1/conv1/Conv_92:out0"], "outputs": ["out0"]}, "Relu_/layer5/layer5.0/relu/Relu_79": {"name": "Relu_/layer5/layer5.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer5/layer5.0/conv1/Conv_80:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.0/conv1/Conv_80": {"name": "Conv_/layer5/layer5.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_3/Relu_58:out0"], "outputs": ["out0"]}, "Relu_/layer4_d/layer4_d.0/relu_1/Relu_81": {"name": "Relu_/layer4_d/layer4_d.0/relu_1/Relu", "op": "relu", "inputs": ["@Conv_/layer4_d/layer4_d.0/conv2/Conv_94:out0"], "outputs": ["out0"]}, "Relu_/relu_5/Relu_82": {"name": "Relu_/relu_5/Relu", "op": "relu", "inputs": ["@Add_/Add_95:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.2/conv2/Conv_83": {"name": "Conv_/layer4/layer4.2/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4/layer4.2/relu/Relu_96:out0"], "outputs": ["out0"]}, "Relu_/layer4/layer4.1/relu_1/Relu_84": {"name": "Relu_/layer4/layer4.1/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer4/layer4.1/Add_97:out0"], "outputs": ["out0"]}, "Conv_/layer4_/layer4_.1/conv1/Conv_85": {"name": "Conv_/layer4_/layer4_.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4_/layer4_.0/relu_1/Relu_60:out0"], "outputs": ["out0"]}, "Conv_/layer4_/layer4_.0/conv2/Conv_86": {"name": "Conv_/layer4_/layer4_.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4_/layer4_.0/relu/Relu_99:out0"], "outputs": ["out0"]}, "Relu_/relu_4/Relu_87": {"name": "Relu_/relu_4/Relu", "op": "relu", "inputs": ["@Add_/pag3/Add_100:out0"], "outputs": ["out0"]}, "Conv_/pag4/f_x/f_x.0/Conv_88": {"name": "Conv_/pag4/f_x/f_x.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer4_/layer4_.1/Add_46:out0"], "outputs": ["out0"]}, "Conv_/pag4/f_y/f_y.0/Conv_89": {"name": "Conv_/pag4/f_y/f_y.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Conv_/compression4/compression4.0/Conv_62:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale0/scale0.0/BatchNormalization_90": {"name": "BatchNormalization_/spp/scale0/scale0.0/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale_process/scale_process.0/BatchNormalization_91": {"name": "BatchNormalization_/spp/scale_process/scale_process.0/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@Concat_/spp/Concat_4_93:out0"], "outputs": ["out0"]}, "Conv_/layer5/layer5.1/conv1/Conv_92": {"name": "Conv_/layer5/layer5.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer5/layer5.0/Add_41:out0"], "outputs": ["out0"]}, "Concat_/spp/Concat_4_93": {"name": "Concat_/spp/Concat_4", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Add_/spp/Add_104:out0", "@Add_/spp/Add_1_105:out0", "@Add_/spp/Add_2_106:out0", "@Add_/spp/Add_3_107:out0"], "outputs": ["out0"]}, "Conv_/layer4_d/layer4_d.0/conv2/Conv_94": {"name": "Conv_/layer4_d/layer4_d.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4_d/layer4_d.0/relu/Relu_108:out0"], "outputs": ["out0"]}, "Add_/Add_95": {"name": "Add_/Add", "op": "add", "inputs": ["@Add_/layer3_d/Add_109:out0", "@Resize_/Resize_110:out0"], "outputs": ["out0"]}, "Relu_/layer4/layer4.2/relu/Relu_96": {"name": "Relu_/layer4/layer4.2/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4/layer4.2/conv1/Conv_111:out0"], "outputs": ["out0"]}, "Add_/layer4/layer4.1/Add_97": {"name": "Add_/layer4/layer4.1/Add", "op": "add", "inputs": ["@Conv_/layer4/layer4.1/conv2/Conv_112:out0", "@Relu_/layer4/layer4.0/relu_1/Relu_98:out0"], "outputs": ["out0"]}, "Relu_/layer4/layer4.0/relu_1/Relu_98": {"name": "Relu_/layer4/layer4.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer4/layer4.0/Add_113:out0"], "outputs": ["out0"]}, "Relu_/layer4_/layer4_.0/relu/Relu_99": {"name": "Relu_/layer4_/layer4_.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4_/layer4_.0/conv1/Conv_114:out0"], "outputs": ["out0"]}, "Add_/pag3/Add_100": {"name": "Add_/pag3/Add", "op": "add", "inputs": ["@Mul_/pag3/Mul_1_115:out0", "@Mul_/pag3/Mul_2_101:out0"], "outputs": ["out0"]}, "Mul_/pag3/Mul_2_101": {"name": "Mul_/pag3/Mul_2", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Sigmoid_/pag3/Sigmoid_116:out0", "@Resize_/pag3/Resize_1_102:out0"], "outputs": ["out0"]}, "Resize_/pag3/Resize_1_102": {"name": "Resize_/pag3/Resize_1", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/compression3/compression3.0/Conv_103:out0"], "outputs": ["out0"]}, "Conv_/compression3/compression3.0/Conv_103": {"name": "Conv_/compression3/compression3.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_2/Relu_117:out0"], "outputs": ["out0"]}, "Add_/spp/Add_104": {"name": "Add_/spp/Add", "op": "add", "inputs": ["@Resize_/spp/Resize_118:out0", "@Conv_/spp/scale0/scale0.2/Conv_63:out0"], "outputs": ["out0"]}, "Add_/spp/Add_1_105": {"name": "Add_/spp/Add_1", "op": "add", "inputs": ["@Resize_/spp/Resize_1_120:out0", "@Conv_/spp/scale0/scale0.2/Conv_63:out0"], "outputs": ["out0"]}, "Add_/spp/Add_2_106": {"name": "Add_/spp/Add_2", "op": "add", "inputs": ["@Resize_/spp/Resize_2_122:out0", "@Conv_/spp/scale0/scale0.2/Conv_63:out0"], "outputs": ["out0"]}, "Add_/spp/Add_3_107": {"name": "Add_/spp/Add_3", "op": "add", "inputs": ["@Resize_/spp/Resize_3_124:out0", "@Conv_/spp/scale0/scale0.2/Conv_63:out0"], "outputs": ["out0"]}, "Relu_/layer4_d/layer4_d.0/relu/Relu_108": {"name": "Relu_/layer4_d/layer4_d.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4_d/layer4_d.0/conv1/Conv_126:out0"], "outputs": ["out0"]}, "Add_/layer3_d/Add_109": {"name": "Add_/layer3_d/Add", "op": "add", "inputs": ["@Conv_/layer3_d/conv2/Conv_127:out0", "@Conv_/layer3_d/downsample/downsample.0/Conv_128:out0"], "outputs": ["out0"]}, "Resize_/Resize_110": {"name": "Resize_/Resize", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/diff3/diff3.0/Conv_129:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.2/conv1/Conv_111": {"name": "Conv_/layer4/layer4.2/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4/layer4.1/relu_1/Relu_84:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.1/conv2/Conv_112": {"name": "Conv_/layer4/layer4.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4/layer4.1/relu/Relu_131:out0"], "outputs": ["out0"]}, "Add_/layer4/layer4.0/Add_113": {"name": "Add_/layer4/layer4.0/Add", "op": "add", "inputs": ["@Conv_/layer4/layer4.0/conv2/Conv_132:out0", "@Conv_/layer4/layer4.0/downsample/downsample.0/Conv_133:out0"], "outputs": ["out0"]}, "Conv_/layer4_/layer4_.0/conv1/Conv_114": {"name": "Conv_/layer4_/layer4_.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_4/Relu_87:out0"], "outputs": ["out0"]}, "Mul_/pag3/Mul_1_115": {"name": "Mul_/pag3/Mul_1", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Mul_/pag3/Mul_1_115_acuity_mark_perm_212:out0", "@Add_/layer3_/layer3_.1/Add_136:out0"], "outputs": ["out0"]}, "Sigmoid_/pag3/Sigmoid_116": {"name": "Sigmoid_/pag3/Sigmoid", "op": "sigmoid", "inputs": ["@ReduceSum_/pag3/ReduceSum_137:out0"], "outputs": ["out0"]}, "Relu_/relu_2/Relu_117": {"name": "Relu_/relu_2/Relu", "op": "relu", "inputs": ["@Add_/layer3/layer3.2/Add_138:out0"], "outputs": ["out0"]}, "Resize_/spp/Resize_118": {"name": "Resize_/spp/Resize", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [16, 32], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/spp/scale1/scale1.3/Conv_119:out0"], "outputs": ["out0"]}, "Conv_/spp/scale1/scale1.3/Conv_119": {"name": "Conv_/spp/scale1/scale1.3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 96, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/spp/scale1/scale1.2/Relu_139:out0"], "outputs": ["out0"]}, "Resize_/spp/Resize_1_120": {"name": "Resize_/spp/Resize_1", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [16, 32], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/spp/scale2/scale2.3/Conv_121:out0"], "outputs": ["out0"]}, "Conv_/spp/scale2/scale2.3/Conv_121": {"name": "Conv_/spp/scale2/scale2.3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 96, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/spp/scale2/scale2.2/Relu_140:out0"], "outputs": ["out0"]}, "Resize_/spp/Resize_2_122": {"name": "Resize_/spp/Resize_2", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [16, 32], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/spp/scale3/scale3.3/Conv_123:out0"], "outputs": ["out0"]}, "Conv_/spp/scale3/scale3.3/Conv_123": {"name": "Conv_/spp/scale3/scale3.3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 96, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/spp/scale3/scale3.2/Relu_141:out0"], "outputs": ["out0"]}, "Resize_/spp/Resize_3_124": {"name": "Resize_/spp/Resize_3", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [16, 32], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/spp/scale4/scale4.3/Conv_125:out0"], "outputs": ["out0"]}, "Conv_/spp/scale4/scale4.3/Conv_125": {"name": "Conv_/spp/scale4/scale4.3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 96, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/spp/scale4/scale4.2/Relu_142:out0"], "outputs": ["out0"]}, "Conv_/layer4_d/layer4_d.0/conv1/Conv_126": {"name": "Conv_/layer4_d/layer4_d.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_5/Relu_82:out0"], "outputs": ["out0"]}, "Conv_/layer3_d/conv2/Conv_127": {"name": "Conv_/layer3_d/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3_d/relu/Relu_144:out0"], "outputs": ["out0"]}, "Conv_/layer3_d/downsample/downsample.0/Conv_128": {"name": "Conv_/layer3_d/downsample/downsample.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/diff3/diff3.0/Conv_129": {"name": "Conv_/diff3/diff3.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_2/Relu_117:out0"], "outputs": ["out0"]}, "Relu_/relu_1/Relu_130": {"name": "Relu_/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer2/layer2.1/Add_145:out0"], "outputs": ["out0"]}, "Relu_/layer4/layer4.1/relu/Relu_131": {"name": "Relu_/layer4/layer4.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4/layer4.1/conv1/Conv_146:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.0/conv2/Conv_132": {"name": "Conv_/layer4/layer4.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4/layer4.0/relu/Relu_134:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.0/downsample/downsample.0/Conv_133": {"name": "Conv_/layer4/layer4.0/downsample/downsample.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 2, "stride_w": 2, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_2/Relu_117:out0"], "outputs": ["out0"]}, "Relu_/layer4/layer4.0/relu/Relu_134": {"name": "Relu_/layer4/layer4.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer4/layer4.0/conv1/Conv_147:out0"], "outputs": ["out0"]}, "Sub_/pag3/Sub_135": {"name": "Sub_/pag3/Sub", "op": "subtract", "inputs": ["@Initializer_/pag3/Constant_12_output_0_22:out0", "@Sub_/pag3/Sub_135_acuity_mark_perm_211:out0"], "outputs": ["out0"]}, "Add_/layer3_/layer3_.1/Add_136": {"name": "Add_/layer3_/layer3_.1/Add", "op": "add", "inputs": ["@Conv_/layer3_/layer3_.1/conv2/Conv_150:out0", "@Relu_/layer3_/layer3_.0/relu_1/Relu_151:out0"], "outputs": ["out0"]}, "ReduceSum_/pag3/ReduceSum_137": {"name": "ReduceSum_/pag3/ReduceSum", "op": "reducesum", "parameters": {"axis_list": [3], "keep_dims": true}, "inputs": ["@Mul_/pag3/Mul_152:out0"], "outputs": ["out0"]}, "Add_/layer3/layer3.2/Add_138": {"name": "Add_/layer3/layer3.2/Add", "op": "add", "inputs": ["@Conv_/layer3/layer3.2/conv2/Conv_153:out0", "@Relu_/layer3/layer3.1/relu_1/Relu_154:out0"], "outputs": ["out0"]}, "Relu_/spp/scale1/scale1.2/Relu_139": {"name": "Relu_/spp/scale1/scale1.2/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale1/scale1.1/BatchNormalization_155:out0"], "outputs": ["out0"]}, "Relu_/spp/scale2/scale2.2/Relu_140": {"name": "Relu_/spp/scale2/scale2.2/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale2/scale2.1/BatchNormalization_156:out0"], "outputs": ["out0"]}, "Relu_/spp/scale3/scale3.2/Relu_141": {"name": "Relu_/spp/scale3/scale3.2/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale3/scale3.1/BatchNormalization_157:out0"], "outputs": ["out0"]}, "Relu_/spp/scale4/scale4.2/Relu_142": {"name": "Relu_/spp/scale4/scale4.2/Relu", "op": "relu", "inputs": ["@BatchNormalization_/spp/scale4/scale4.1/BatchNormalization_143:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale4/scale4.1/BatchNormalization_143": {"name": "BatchNormalization_/spp/scale4/scale4.1/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@GlobalAveragePool_/spp/scale4/scale4.0/GlobalAveragePool_158:out0"], "outputs": ["out0"]}, "Relu_/layer3_d/relu/Relu_144": {"name": "Relu_/layer3_d/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3_d/conv1/Conv_159:out0"], "outputs": ["out0"]}, "Add_/layer2/layer2.1/Add_145": {"name": "Add_/layer2/layer2.1/Add", "op": "add", "inputs": ["@Conv_/layer2/layer2.1/conv2/Conv_160:out0", "@Relu_/layer2/layer2.0/relu_1/Relu_148:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.1/conv1/Conv_146": {"name": "Conv_/layer4/layer4.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer4/layer4.0/relu_1/Relu_98:out0"], "outputs": ["out0"]}, "Conv_/layer4/layer4.0/conv1/Conv_147": {"name": "Conv_/layer4/layer4.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_2/Relu_117:out0"], "outputs": ["out0"]}, "Relu_/layer2/layer2.0/relu_1/Relu_148": {"name": "Relu_/layer2/layer2.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer2/layer2.0/Add_149:out0"], "outputs": ["out0"]}, "Add_/layer2/layer2.0/Add_149": {"name": "Add_/layer2/layer2.0/Add", "op": "add", "inputs": ["@Conv_/layer2/layer2.0/conv2/Conv_161:out0", "@Conv_/layer2/layer2.0/downsample/downsample.0/Conv_162:out0"], "outputs": ["out0"]}, "Conv_/layer3_/layer3_.1/conv2/Conv_150": {"name": "Conv_/layer3_/layer3_.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3_/layer3_.1/relu/Relu_163:out0"], "outputs": ["out0"]}, "Relu_/layer3_/layer3_.0/relu_1/Relu_151": {"name": "Relu_/layer3_/layer3_.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer3_/layer3_.0/Add_164:out0"], "outputs": ["out0"]}, "Mul_/pag3/Mul_152": {"name": "Mul_/pag3/Mul", "op": "multiply", "parameters": {"axis": 3, "bias": true}, "inputs": ["@Conv_/pag3/f_x/f_x.0/Conv_165:out0", "@Resize_/pag3/Resize_166:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.2/conv2/Conv_153": {"name": "Conv_/layer3/layer3.2/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3/layer3.2/relu/Relu_167:out0"], "outputs": ["out0"]}, "Relu_/layer3/layer3.1/relu_1/Relu_154": {"name": "Relu_/layer3/layer3.1/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer3/layer3.1/Add_168:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale1/scale1.1/BatchNormalization_155": {"name": "BatchNormalization_/spp/scale1/scale1.1/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@AveragePool_/spp/scale1/scale1.0/AveragePool_169:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale2/scale2.1/BatchNormalization_156": {"name": "BatchNormalization_/spp/scale2/scale2.1/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@AveragePool_/spp/scale2/scale2.0/AveragePool_170:out0"], "outputs": ["out0"]}, "BatchNormalization_/spp/scale3/scale3.1/BatchNormalization_157": {"name": "BatchNormalization_/spp/scale3/scale3.1/BatchNormalization", "op": "batchnormalize", "parameters": {"eps": 9.999999747378752e-06}, "inputs": ["@AveragePool_/spp/scale3/scale3.0/AveragePool_171:out0"], "outputs": ["out0"]}, "GlobalAveragePool_/spp/scale4/scale4.0/GlobalAveragePool_158": {"name": "GlobalAveragePool_/spp/scale4/scale4.0/GlobalAveragePool", "op": "pooling", "parameters": {"padding": "VALID", "type": "AVG", "ksize_h": 16, "ksize_w": 32, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "round_type": "floor", "pad_method": "padding_const", "pad": [0, 0, 0, 0], "count_include_pad": 1}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "Conv_/layer3_d/conv1/Conv_159": {"name": "Conv_/layer3_d/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/layer2/layer2.1/conv2/Conv_160": {"name": "Conv_/layer2/layer2.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer2/layer2.1/relu/Relu_174:out0"], "outputs": ["out0"]}, "Conv_/layer2/layer2.0/conv2/Conv_161": {"name": "Conv_/layer2/layer2.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer2/layer2.0/relu/Relu_175:out0"], "outputs": ["out0"]}, "Conv_/layer2/layer2.0/downsample/downsample.0/Conv_162": {"name": "Conv_/layer2/layer2.0/downsample/downsample.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 2, "stride_w": 2, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu/Relu_176:out0"], "outputs": ["out0"]}, "Relu_/layer3_/layer3_.1/relu/Relu_163": {"name": "Relu_/layer3_/layer3_.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3_/layer3_.1/conv1/Conv_177:out0"], "outputs": ["out0"]}, "Add_/layer3_/layer3_.0/Add_164": {"name": "Add_/layer3_/layer3_.0/Add", "op": "add", "inputs": ["@Conv_/layer3_/layer3_.0/conv2/Conv_178:out0", "@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/pag3/f_x/f_x.0/Conv_165": {"name": "Conv_/pag3/f_x/f_x.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer3_/layer3_.1/Add_136:out0"], "outputs": ["out0"]}, "Resize_/pag3/Resize_166": {"name": "Resize_/pag3/Resize", "op": "image_resize", "parameters": {"type": "bilinear", "new_size": [128, 256], "align_corners": false, "half_pixel": true}, "inputs": ["@Conv_/pag3/f_y/f_y.0/Conv_181:out0"], "outputs": ["out0"]}, "Relu_/layer3/layer3.2/relu/Relu_167": {"name": "Relu_/layer3/layer3.2/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3/layer3.2/conv1/Conv_182:out0"], "outputs": ["out0"]}, "Add_/layer3/layer3.1/Add_168": {"name": "Add_/layer3/layer3.1/Add", "op": "add", "inputs": ["@Conv_/layer3/layer3.1/conv2/Conv_183:out0", "@Relu_/layer3/layer3.0/relu_1/Relu_172:out0"], "outputs": ["out0"]}, "AveragePool_/spp/scale1/scale1.0/AveragePool_169": {"name": "AveragePool_/spp/scale1/scale1.0/AveragePool", "op": "pooling", "parameters": {"padding": "VALID", "type": "AVG", "ksize_h": 5, "ksize_w": 5, "stride_h": 2, "stride_w": 2, "pad_h": 2, "pad_w": 2, "round_type": "floor", "pad_method": "padding_const", "pad": [2, 2, 2, 2], "count_include_pad": 1}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "AveragePool_/spp/scale2/scale2.0/AveragePool_170": {"name": "AveragePool_/spp/scale2/scale2.0/AveragePool", "op": "pooling", "parameters": {"padding": "VALID", "type": "AVG", "ksize_h": 9, "ksize_w": 9, "stride_h": 4, "stride_w": 4, "pad_h": 4, "pad_w": 4, "round_type": "floor", "pad_method": "padding_const", "pad": [4, 4, 4, 4], "count_include_pad": 1}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "AveragePool_/spp/scale3/scale3.0/AveragePool_171": {"name": "AveragePool_/spp/scale3/scale3.0/AveragePool", "op": "pooling", "parameters": {"padding": "VALID", "type": "AVG", "ksize_h": 17, "ksize_w": 17, "stride_h": 8, "stride_w": 8, "pad_h": 8, "pad_w": 8, "round_type": "floor", "pad_method": "padding_const", "pad": [8, 8, 8, 8], "count_include_pad": 1}, "inputs": ["@Add_/layer5/layer5.1/Add_33:out0"], "outputs": ["out0"]}, "Relu_/layer3/layer3.0/relu_1/Relu_172": {"name": "Relu_/layer3/layer3.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer3/layer3.0/Add_173:out0"], "outputs": ["out0"]}, "Add_/layer3/layer3.0/Add_173": {"name": "Add_/layer3/layer3.0/Add", "op": "add", "inputs": ["@Conv_/layer3/layer3.0/conv2/Conv_184:out0", "@Conv_/layer3/layer3.0/downsample/downsample.0/Conv_185:out0"], "outputs": ["out0"]}, "Relu_/layer2/layer2.1/relu/Relu_174": {"name": "Relu_/layer2/layer2.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer2/layer2.1/conv1/Conv_186:out0"], "outputs": ["out0"]}, "Relu_/layer2/layer2.0/relu/Relu_175": {"name": "Relu_/layer2/layer2.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer2/layer2.0/conv1/Conv_187:out0"], "outputs": ["out0"]}, "Relu_/relu/Relu_176": {"name": "Relu_/relu/Relu", "op": "relu", "inputs": ["@Add_/layer1/layer1.1/Add_188:out0"], "outputs": ["out0"]}, "Conv_/layer3_/layer3_.1/conv1/Conv_177": {"name": "Conv_/layer3_/layer3_.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3_/layer3_.0/relu_1/Relu_151:out0"], "outputs": ["out0"]}, "Conv_/layer3_/layer3_.0/conv2/Conv_178": {"name": "Conv_/layer3_/layer3_.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3_/layer3_.0/relu/Relu_179:out0"], "outputs": ["out0"]}, "Relu_/layer3_/layer3_.0/relu/Relu_179": {"name": "Relu_/layer3_/layer3_.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3_/layer3_.0/conv1/Conv_180:out0"], "outputs": ["out0"]}, "Conv_/layer3_/layer3_.0/conv1/Conv_180": {"name": "Conv_/layer3_/layer3_.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/pag3/f_y/f_y.0/Conv_181": {"name": "Conv_/pag3/f_y/f_y.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Conv_/compression3/compression3.0/Conv_103:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.2/conv1/Conv_182": {"name": "Conv_/layer3/layer3.2/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3/layer3.1/relu_1/Relu_154:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.1/conv2/Conv_183": {"name": "Conv_/layer3/layer3.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3/layer3.1/relu/Relu_193:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.0/conv2/Conv_184": {"name": "Conv_/layer3/layer3.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3/layer3.0/relu/Relu_194:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.0/downsample/downsample.0/Conv_185": {"name": "Conv_/layer3/layer3.0/downsample/downsample.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 2, "stride_w": 2, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/layer2/layer2.1/conv1/Conv_186": {"name": "Conv_/layer2/layer2.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer2/layer2.0/relu_1/Relu_148:out0"], "outputs": ["out0"]}, "Conv_/layer2/layer2.0/conv1/Conv_187": {"name": "Conv_/layer2/layer2.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu/Relu_176:out0"], "outputs": ["out0"]}, "Add_/layer1/layer1.1/Add_188": {"name": "Add_/layer1/layer1.1/Add", "op": "add", "inputs": ["@Conv_/layer1/layer1.1/conv2/Conv_198:out0", "@Relu_/layer1/layer1.0/relu_1/Relu_189:out0"], "outputs": ["out0"]}, "Relu_/layer1/layer1.0/relu_1/Relu_189": {"name": "Relu_/layer1/layer1.0/relu_1/Relu", "op": "relu", "inputs": ["@Add_/layer1/layer1.0/Add_190:out0"], "outputs": ["out0"]}, "Add_/layer1/layer1.0/Add_190": {"name": "Add_/layer1/layer1.0/Add", "op": "add", "inputs": ["@Conv_/layer1/layer1.0/conv2/Conv_199:out0", "@Relu_/conv1/conv1.5/Relu_191:out0"], "outputs": ["out0"]}, "Relu_/conv1/conv1.5/Relu_191": {"name": "Relu_/conv1/conv1.5/Relu", "op": "relu", "inputs": ["@Conv_/conv1/conv1.3/Conv_192:out0"], "outputs": ["out0"]}, "Conv_/conv1/conv1.3/Conv_192": {"name": "Conv_/conv1/conv1.3/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/conv1/conv1.2/Relu_197:out0"], "outputs": ["out0"]}, "Relu_/layer3/layer3.1/relu/Relu_193": {"name": "Relu_/layer3/layer3.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3/layer3.1/conv1/Conv_196:out0"], "outputs": ["out0"]}, "Relu_/layer3/layer3.0/relu/Relu_194": {"name": "Relu_/layer3/layer3.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer3/layer3.0/conv1/Conv_195:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.0/conv1/Conv_195": {"name": "Conv_/layer3/layer3.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/relu_1/Relu_130:out0"], "outputs": ["out0"]}, "Conv_/layer3/layer3.1/conv1/Conv_196": {"name": "Conv_/layer3/layer3.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer3/layer3.0/relu_1/Relu_172:out0"], "outputs": ["out0"]}, "Relu_/conv1/conv1.2/Relu_197": {"name": "Relu_/conv1/conv1.2/Relu", "op": "relu", "inputs": ["@Conv_/conv1/conv1.0/Conv_200:out0"], "outputs": ["out0"]}, "Conv_/layer1/layer1.1/conv2/Conv_198": {"name": "Conv_/layer1/layer1.1/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer1/layer1.1/relu/Relu_201:out0"], "outputs": ["out0"]}, "Conv_/layer1/layer1.0/conv2/Conv_199": {"name": "Conv_/layer1/layer1.0/conv2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer1/layer1.0/relu/Relu_202:out0"], "outputs": ["out0"]}, "Conv_/conv1/conv1.0/Conv_200": {"name": "Conv_/conv1/conv1.0/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Conv_/conv1/conv1.0/Conv_200_acuity_mark_perm_213:out0"], "outputs": ["out0"]}, "Relu_/layer1/layer1.1/relu/Relu_201": {"name": "Relu_/layer1/layer1.1/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer1/layer1.1/conv1/Conv_204:out0"], "outputs": ["out0"]}, "Relu_/layer1/layer1.0/relu/Relu_202": {"name": "Relu_/layer1/layer1.0/relu/Relu", "op": "relu", "inputs": ["@Conv_/layer1/layer1.0/conv1/Conv_203:out0"], "outputs": ["out0"]}, "Conv_/layer1/layer1.0/conv1/Conv_203": {"name": "Conv_/layer1/layer1.0/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/conv1/conv1.5/Relu_191:out0"], "outputs": ["out0"]}, "Conv_/layer1/layer1.1/conv1/Conv_204": {"name": "Conv_/layer1/layer1.1/conv1/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Relu_/layer1/layer1.0/relu_1/Relu_189:out0"], "outputs": ["out0"]}, "input_205": {"name": "input", "op": "input", "parameters": {"size": "", "channels": 1, "shape": [1, 3, 1024, 2048], "is_scalar": false, "type": "float32"}, "inputs": [], "outputs": ["out0"]}, "attach_Conv_/final_layer/conv2/Conv/out0_0_acuity_mark_perm_206": {"name": "attach_Conv_/final_layer/conv2/Conv/out0_0_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/final_layer/conv2/Conv_1:out0"], "outputs": ["out0"]}, "Mul_/dfm/Mul_11_acuity_mark_perm_207": {"name": "Mul_/dfm/Mul_11_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 2, 3, 1]}, "inputs": ["@Sub_/dfm/Sub_15:out0"], "outputs": ["out0"]}, "Sub_/dfm/Sub_15_acuity_mark_perm_208": {"name": "Sub_/dfm/Sub_15_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Sigmoid_/dfm/Sigmoid_16:out0"], "outputs": ["out0"]}, "Mul_/pag4/Mul_1_37_acuity_mark_perm_209": {"name": "Mul_/pag4/Mul_1_37_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 2, 3, 1]}, "inputs": ["@Sub_/pag4/Sub_45:out0"], "outputs": ["out0"]}, "Sub_/pag4/Sub_45_acuity_mark_perm_210": {"name": "Sub_/pag4/Sub_45_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Sigmoid_/pag4/Sigmoid_47:out0"], "outputs": ["out0"]}, "Sub_/pag3/Sub_135_acuity_mark_perm_211": {"name": "Sub_/pag3/Sub_135_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Sigmoid_/pag3/Sigmoid_116:out0"], "outputs": ["out0"]}, "Mul_/pag3/Mul_1_115_acuity_mark_perm_212": {"name": "Mul_/pag3/Mul_1_115_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 2, 3, 1]}, "inputs": ["@Sub_/pag3/Sub_135:out0"], "outputs": ["out0"]}, "Conv_/conv1/conv1.0/Conv_200_acuity_mark_perm_213": {"name": "Conv_/conv1/conv1.0/Conv_200_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 2, 3, 1]}, "inputs": ["@input_205:out0"], "outputs": ["out0"]}, "Add_/dfm/Add_2_6_concat_214": {"name": "Add_/dfm/Add_2_6_concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Add_/dfm/Add_9:out0", "@Add_/dfm/Add_1_10:out0"], "outputs": ["out0"]}, "Add_/dfm/Add_2_6_conv_215": {"name": "Add_/dfm/Add_2_6_conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/dfm/Add_2_6_concat_214:out0"], "outputs": ["out0"]}, "Add_/layer5_/layer5_.0/Add_12_concat_216": {"name": "Add_/layer5_/layer5_.0/Add_12_concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Relu_/layer5_/layer5_.0/relu_1/Relu_24:out0", "@Relu_/relu_6/Relu_25:out0"], "outputs": ["out0"]}, "Add_/layer5_/layer5_.0/Add_12_conv_217": {"name": "Add_/layer5_/layer5_.0/Add_12_conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer5_/layer5_.0/Add_12_concat_216:out0"], "outputs": ["out0"]}, "Add_/spp/Add_4_19_concat_218": {"name": "Add_/spp/Add_4_19_concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Relu_/spp/compression/compression.1/Relu_32:out0", "@Relu_/spp/shortcut/shortcut.1/Relu_21:out0"], "outputs": ["out0"]}, "Add_/spp/Add_4_19_conv_219": {"name": "Add_/spp/Add_4_19_conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/spp/Add_4_19_concat_218:out0"], "outputs": ["out0"]}, "Add_/layer5_d/layer5_d.0/Add_23_concat_220": {"name": "Add_/layer5_d/layer5_d.0/Add_23_concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Relu_/layer5_d/layer5_d.0/relu_1/Relu_34:out0", "@Relu_/relu_7/Relu_35:out0"], "outputs": ["out0"]}, "Add_/layer5_d/layer5_d.0/Add_23_conv_221": {"name": "Add_/layer5_d/layer5_d.0/Add_23_conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer5_d/layer5_d.0/Add_23_concat_220:out0"], "outputs": ["out0"]}, "Add_/layer4_d/layer4_d.0/Add_54_concat_222": {"name": "Add_/layer4_d/layer4_d.0/Add_54_concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Relu_/layer4_d/layer4_d.0/relu_1/Relu_81:out0", "@Relu_/relu_5/Relu_82:out0"], "outputs": ["out0"]}, "Add_/layer4_d/layer4_d.0/Add_54_conv_223": {"name": "Add_/layer4_d/layer4_d.0/Add_54_conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Add_/layer4_d/layer4_d.0/Add_54_concat_222:out0"], "outputs": ["out0"]}}, "quantize_info": {}}
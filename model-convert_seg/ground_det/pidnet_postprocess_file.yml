# "acuity_postprocs" allowed types: "classification_validate, detection_validate, dump_results, print_topn, classification_classic, mute_built_in_actions"
postprocess:
  acuity_postprocs:
    print_topn:
      topn: 5
    dump_results:
      file_type: TENSOR
    # mute_built_in_actions: true
    # python:
    #   file_path: postprocess.py
    #   parameters:
    #     output_tensors:
    #     - '@xxxx:out0'
  app_postprocs:
  - lid: attach_Conv_/final_layer/conv2/Conv/out0_0
    postproc_params:
      add_postproc_node: true
      perm:
      - 0
      - 1
      - 2
      - 3
      force_float32: true

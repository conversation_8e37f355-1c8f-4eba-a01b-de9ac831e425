{"MetaData": {"Name": "Ovxlib_Debug_Graph", "AcuityVersion": "UNKNOWN", "Platform": "UNKNOWN", "Org_Platform": "UNKNOWN"}, "Layers": {"uid_22": {"op": "VARIABLE", "inputs": ["@data_input_uid_201:out0"], "inut_shape": [[1]], "outputs": ["out0"], "output_shape": [[1]]}, "uid_200": {"op": "CONV2D", "inputs": ["@uid_30000:out0", "@data_input_uid_202:out0", "@data_input_uid_203:out0"], "inut_shape": [[2048, 1024, 3, 1], [3, 3, 3, 32], [32]], "outputs": ["out0"], "output_shape": [[1024, 512, 32, 1]]}, "uid_197": {"op": "RELU", "inputs": ["@uid_200:out0"], "inut_shape": [[1024, 512, 32, 1]], "outputs": ["out0"], "output_shape": [[1024, 512, 32, 1]]}, "uid_192": {"op": "CONV2D", "inputs": ["@uid_197:out0", "@data_input_uid_204:out0", "@data_input_uid_205:out0"], "inut_shape": [[1024, 512, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_191": {"op": "RELU", "inputs": ["@uid_192:out0"], "inut_shape": [[512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_203": {"op": "CONV2D", "inputs": ["@uid_191:out0", "@data_input_uid_206:out0", "@data_input_uid_207:out0"], "inut_shape": [[512, 256, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_202": {"op": "RELU", "inputs": ["@uid_203:out0"], "inut_shape": [[512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_199": {"op": "CONV2D", "inputs": ["@uid_202:out0", "@data_input_uid_208:out0", "@data_input_uid_209:out0"], "inut_shape": [[512, 256, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_190": {"op": "ADD", "inputs": ["@uid_199:out0", "@uid_191:out0"], "inut_shape": [[512, 256, 32, 1], [512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_189": {"op": "RELU", "inputs": ["@uid_190:out0"], "inut_shape": [[512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_204": {"op": "CONV2D", "inputs": ["@uid_189:out0", "@data_input_uid_210:out0", "@data_input_uid_211:out0"], "inut_shape": [[512, 256, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_201": {"op": "RELU", "inputs": ["@uid_204:out0"], "inut_shape": [[512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_198": {"op": "CONV2D", "inputs": ["@uid_201:out0", "@data_input_uid_212:out0", "@data_input_uid_213:out0"], "inut_shape": [[512, 256, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_188": {"op": "ADD", "inputs": ["@uid_198:out0", "@uid_189:out0"], "inut_shape": [[512, 256, 32, 1], [512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_176": {"op": "RELU", "inputs": ["@uid_188:out0"], "inut_shape": [[512, 256, 32, 1]], "outputs": ["out0"], "output_shape": [[512, 256, 32, 1]]}, "uid_162": {"op": "CONV2D", "inputs": ["@uid_176:out0", "@data_input_uid_214:out0", "@data_input_uid_215:out0"], "inut_shape": [[512, 256, 32, 1], [1, 1, 32, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_187": {"op": "CONV2D", "inputs": ["@uid_176:out0", "@data_input_uid_216:out0", "@data_input_uid_217:out0"], "inut_shape": [[512, 256, 32, 1], [3, 3, 32, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_175": {"op": "RELU", "inputs": ["@uid_187:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_161": {"op": "CONV2D", "inputs": ["@uid_175:out0", "@data_input_uid_218:out0", "@data_input_uid_219:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_149": {"op": "ADD", "inputs": ["@uid_161:out0", "@uid_162:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_148": {"op": "RELU", "inputs": ["@uid_149:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_186": {"op": "CONV2D", "inputs": ["@uid_148:out0", "@data_input_uid_220:out0", "@data_input_uid_221:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_174": {"op": "RELU", "inputs": ["@uid_186:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_160": {"op": "CONV2D", "inputs": ["@uid_174:out0", "@data_input_uid_222:out0", "@data_input_uid_223:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_145": {"op": "ADD", "inputs": ["@uid_160:out0", "@uid_148:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_130": {"op": "RELU", "inputs": ["@uid_145:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_128": {"op": "CONV2D", "inputs": ["@uid_130:out0", "@data_input_uid_224:out0", "@data_input_uid_225:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_159": {"op": "CONV2D", "inputs": ["@uid_130:out0", "@data_input_uid_226:out0", "@data_input_uid_227:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_180": {"op": "CONV2D", "inputs": ["@uid_130:out0", "@data_input_uid_228:out0", "@data_input_uid_229:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_185": {"op": "CONV2D", "inputs": ["@uid_130:out0", "@data_input_uid_230:out0", "@data_input_uid_231:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_195": {"op": "CONV2D", "inputs": ["@uid_130:out0", "@data_input_uid_232:out0", "@data_input_uid_233:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_144": {"op": "RELU", "inputs": ["@uid_159:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_179": {"op": "RELU", "inputs": ["@uid_180:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_194": {"op": "RELU", "inputs": ["@uid_195:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_127": {"op": "CONV2D", "inputs": ["@uid_144:out0", "@data_input_uid_234:out0", "@data_input_uid_235:out0"], "inut_shape": [[256, 128, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_178": {"op": "CONV2D", "inputs": ["@uid_179:out0", "@data_input_uid_236:out0", "@data_input_uid_237:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_184": {"op": "CONV2D", "inputs": ["@uid_194:out0", "@data_input_uid_238:out0", "@data_input_uid_239:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_109": {"op": "ADD", "inputs": ["@uid_127:out0", "@uid_128:out0"], "inut_shape": [[256, 128, 32, 1], [256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_164": {"op": "ADD", "inputs": ["@uid_178:out0", "@uid_130:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_173": {"op": "ADD", "inputs": ["@uid_184:out0", "@uid_185:out0"], "inut_shape": [[128, 64, 128, 1], [128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_151": {"op": "RELU", "inputs": ["@uid_164:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_172": {"op": "RELU", "inputs": ["@uid_173:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_177": {"op": "CONV2D", "inputs": ["@uid_151:out0", "@data_input_uid_240:out0", "@data_input_uid_241:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_196": {"op": "CONV2D", "inputs": ["@uid_172:out0", "@data_input_uid_242:out0", "@data_input_uid_243:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_163": {"op": "RELU", "inputs": ["@uid_177:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_193": {"op": "RELU", "inputs": ["@uid_196:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_150": {"op": "CONV2D", "inputs": ["@uid_163:out0", "@data_input_uid_244:out0", "@data_input_uid_245:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_183": {"op": "CONV2D", "inputs": ["@uid_193:out0", "@data_input_uid_246:out0", "@data_input_uid_247:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_136": {"op": "ADD", "inputs": ["@uid_150:out0", "@uid_151:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_168": {"op": "ADD", "inputs": ["@uid_183:out0", "@uid_172:out0"], "inut_shape": [[128, 64, 128, 1], [128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_165": {"op": "CONV2D", "inputs": ["@uid_136:out0", "@data_input_uid_248:out0", "@data_input_uid_249:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_154": {"op": "RELU", "inputs": ["@uid_168:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_182": {"op": "CONV2D", "inputs": ["@uid_154:out0", "@data_input_uid_250:out0", "@data_input_uid_251:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_167": {"op": "RELU", "inputs": ["@uid_182:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_153": {"op": "CONV2D", "inputs": ["@uid_167:out0", "@data_input_uid_252:out0", "@data_input_uid_253:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_138": {"op": "ADD", "inputs": ["@uid_153:out0", "@uid_154:out0"], "inut_shape": [[128, 64, 128, 1], [128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_117": {"op": "RELU", "inputs": ["@uid_138:out0"], "inut_shape": [[128, 64, 128, 1]], "outputs": ["out0"], "output_shape": [[128, 64, 128, 1]]}, "uid_103": {"op": "CONV2D", "inputs": ["@uid_117:out0", "@data_input_uid_254:out0", "@data_input_uid_255:out0"], "inut_shape": [[128, 64, 128, 1], [1, 1, 128, 64], [64]], "outputs": ["out0"], "output_shape": [[128, 64, 64, 1]]}, "uid_129": {"op": "CONV2D", "inputs": ["@uid_117:out0", "@data_input_uid_256:out0", "@data_input_uid_257:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 32], [32]], "outputs": ["out0"], "output_shape": [[128, 64, 32, 1]]}, "uid_133": {"op": "CONV2D", "inputs": ["@uid_117:out0", "@data_input_uid_258:out0", "@data_input_uid_259:out0"], "inut_shape": [[128, 64, 128, 1], [1, 1, 128, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_147": {"op": "CONV2D", "inputs": ["@uid_117:out0", "@data_input_uid_260:out0", "@data_input_uid_261:out0"], "inut_shape": [[128, 64, 128, 1], [3, 3, 128, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_102": {"op": "RESIZE", "inputs": ["@uid_103:out0"], "inut_shape": [[128, 64, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_181": {"op": "CONV2D", "inputs": ["@uid_103:out0", "@data_input_uid_262:out0", "@data_input_uid_263:out0"], "inut_shape": [[128, 64, 64, 1], [1, 1, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[128, 64, 32, 1]]}, "uid_110": {"op": "RESIZE", "inputs": ["@uid_129:out0"], "inut_shape": [[128, 64, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_134": {"op": "RELU", "inputs": ["@uid_147:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_166": {"op": "RESIZE", "inputs": ["@uid_181:out0"], "inut_shape": [[128, 64, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_95": {"op": "ADD", "inputs": ["@uid_109:out0", "@uid_110:out0"], "inut_shape": [[256, 128, 32, 1], [256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_132": {"op": "CONV2D", "inputs": ["@uid_134:out0", "@data_input_uid_264:out0", "@data_input_uid_265:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_152": {"op": "MULTIPLY", "inputs": ["@uid_165:out0", "@uid_166:out0"], "inut_shape": [[256, 128, 32, 1], [256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_82": {"op": "RELU", "inputs": ["@uid_95:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_113": {"op": "ADD", "inputs": ["@uid_132:out0", "@uid_133:out0"], "inut_shape": [[64, 32, 256, 1], [64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_137": {"op": "REDUCE", "inputs": ["@uid_152:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_126": {"op": "CONV2D", "inputs": ["@uid_82:out0", "@data_input_uid_266:out0", "@data_input_uid_267:out0"], "inut_shape": [[256, 128, 32, 1], [1, 1, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_98": {"op": "RELU", "inputs": ["@uid_113:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_116": {"op": "SIGMOID", "inputs": ["@uid_137:out0"], "inut_shape": [[256, 128, 1, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_108": {"op": "RELU", "inputs": ["@uid_126:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_146": {"op": "CONV2D", "inputs": ["@uid_98:out0", "@data_input_uid_268:out0", "@data_input_uid_269:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_101": {"op": "MULTIPLY", "inputs": ["@uid_116:out0", "@uid_102:out0"], "inut_shape": [[256, 128, 1, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_135": {"op": "SUBTRACT", "inputs": ["@uid_22:out0", "@uid_116:out0"], "inut_shape": [[1], [256, 128, 1, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_94": {"op": "CONV2D", "inputs": ["@uid_108:out0", "@data_input_uid_270:out0", "@data_input_uid_271:out0"], "inut_shape": [[256, 128, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_131": {"op": "RELU", "inputs": ["@uid_146:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_115": {"op": "MULTIPLY", "inputs": ["@uid_135:out0", "@uid_136:out0"], "inut_shape": [[256, 128, 1, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_81": {"op": "RELU", "inputs": ["@uid_94:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_112": {"op": "CONV2D", "inputs": ["@uid_131:out0", "@data_input_uid_272:out0", "@data_input_uid_273:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_100": {"op": "ADD", "inputs": ["@uid_115:out0", "@uid_101:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_222": {"op": "CONCAT", "inputs": ["@uid_81:out0", "@uid_82:out0"], "inut_shape": [[256, 128, 32, 1], [256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_97": {"op": "ADD", "inputs": ["@uid_112:out0", "@uid_98:out0"], "inut_shape": [[64, 32, 256, 1], [64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_87": {"op": "RELU", "inputs": ["@uid_100:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_223": {"op": "CONV2D", "inputs": ["@uid_222:out0", "@data_input_uid_274:out0", "@data_input_uid_275:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_84": {"op": "RELU", "inputs": ["@uid_97:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_114": {"op": "CONV2D", "inputs": ["@uid_87:out0", "@data_input_uid_276:out0", "@data_input_uid_277:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_111": {"op": "CONV2D", "inputs": ["@uid_84:out0", "@data_input_uid_278:out0", "@data_input_uid_279:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_99": {"op": "RELU", "inputs": ["@uid_114:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_96": {"op": "RELU", "inputs": ["@uid_111:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_86": {"op": "CONV2D", "inputs": ["@uid_99:out0", "@data_input_uid_280:out0", "@data_input_uid_281:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_83": {"op": "CONV2D", "inputs": ["@uid_96:out0", "@data_input_uid_282:out0", "@data_input_uid_283:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_73": {"op": "ADD", "inputs": ["@uid_86:out0", "@uid_87:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_71": {"op": "ADD", "inputs": ["@uid_83:out0", "@uid_84:out0"], "inut_shape": [[64, 32, 256, 1], [64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_60": {"op": "RELU", "inputs": ["@uid_73:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_58": {"op": "RELU", "inputs": ["@uid_71:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_85": {"op": "CONV2D", "inputs": ["@uid_60:out0", "@data_input_uid_284:out0", "@data_input_uid_285:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_52": {"op": "CONV2D", "inputs": ["@uid_58:out0", "@data_input_uid_286:out0", "@data_input_uid_287:out0"], "inut_shape": [[64, 32, 256, 1], [1, 1, 256, 512], [512]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_56": {"op": "CONV2D", "inputs": ["@uid_58:out0", "@data_input_uid_288:out0", "@data_input_uid_289:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 64], [64]], "outputs": ["out0"], "output_shape": [[64, 32, 64, 1]]}, "uid_62": {"op": "CONV2D", "inputs": ["@uid_58:out0", "@data_input_uid_290:out0", "@data_input_uid_291:out0"], "inut_shape": [[64, 32, 256, 1], [1, 1, 256, 64], [64]], "outputs": ["out0"], "output_shape": [[64, 32, 64, 1]]}, "uid_80": {"op": "CONV2D", "inputs": ["@uid_58:out0", "@data_input_uid_292:out0", "@data_input_uid_293:out0"], "inut_shape": [[64, 32, 256, 1], [1, 1, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_72": {"op": "RELU", "inputs": ["@uid_85:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_55": {"op": "RESIZE", "inputs": ["@uid_56:out0"], "inut_shape": [[64, 32, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_48": {"op": "RESIZE", "inputs": ["@uid_62:out0"], "inut_shape": [[64, 32, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_89": {"op": "CONV2D", "inputs": ["@uid_62:out0", "@data_input_uid_294:out0", "@data_input_uid_295:out0"], "inut_shape": [[64, 32, 64, 1], [1, 1, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[64, 32, 32, 1]]}, "uid_79": {"op": "RELU", "inputs": ["@uid_80:out0"], "inut_shape": [[64, 32, 256, 1]], "outputs": ["out0"], "output_shape": [[64, 32, 256, 1]]}, "uid_59": {"op": "CONV2D", "inputs": ["@uid_72:out0", "@data_input_uid_296:out0", "@data_input_uid_297:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_43": {"op": "ADD", "inputs": ["@uid_223:out0", "@uid_55:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_75": {"op": "RESIZE", "inputs": ["@uid_89:out0"], "inut_shape": [[64, 32, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_67": {"op": "CONV2D", "inputs": ["@uid_79:out0", "@data_input_uid_298:out0", "@data_input_uid_299:out0"], "inut_shape": [[64, 32, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_46": {"op": "ADD", "inputs": ["@uid_59:out0", "@uid_60:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_35": {"op": "RELU", "inputs": ["@uid_43:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_66": {"op": "RELU", "inputs": ["@uid_67:out0"], "inut_shape": [[32, 16, 256, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_88": {"op": "CONV2D", "inputs": ["@uid_46:out0", "@data_input_uid_300:out0", "@data_input_uid_301:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 32], [32]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_68": {"op": "CONV2D", "inputs": ["@uid_35:out0", "@data_input_uid_302:out0", "@data_input_uid_303:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_51": {"op": "CONV2D", "inputs": ["@uid_66:out0", "@data_input_uid_304:out0", "@data_input_uid_305:out0"], "inut_shape": [[32, 16, 256, 1], [1, 1, 256, 512], [512]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_74": {"op": "MULTIPLY", "inputs": ["@uid_88:out0", "@uid_75:out0"], "inut_shape": [[256, 128, 32, 1], [256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 32, 1]]}, "uid_53": {"op": "RELU", "inputs": ["@uid_68:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_41": {"op": "ADD", "inputs": ["@uid_51:out0", "@uid_52:out0"], "inut_shape": [[32, 16, 512, 1], [32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_61": {"op": "REDUCE", "inputs": ["@uid_74:out0"], "inut_shape": [[256, 128, 32, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_42": {"op": "CONV2D", "inputs": ["@uid_53:out0", "@data_input_uid_306:out0", "@data_input_uid_307:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_92": {"op": "CONV2D", "inputs": ["@uid_41:out0", "@data_input_uid_308:out0", "@data_input_uid_309:out0"], "inut_shape": [[32, 16, 512, 1], [1, 1, 512, 256], [256]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_47": {"op": "SIGMOID", "inputs": ["@uid_61:out0"], "inut_shape": [[256, 128, 1, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_34": {"op": "RELU", "inputs": ["@uid_42:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_78": {"op": "RELU", "inputs": ["@uid_92:out0"], "inut_shape": [[32, 16, 256, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_38": {"op": "MULTIPLY", "inputs": ["@uid_47:out0", "@uid_48:out0"], "inut_shape": [[256, 128, 1, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_45": {"op": "SUBTRACT", "inputs": ["@uid_22:out0", "@uid_47:out0"], "inut_shape": [[1], [256, 128, 1, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 1, 1]]}, "uid_220": {"op": "CONCAT", "inputs": ["@uid_34:out0", "@uid_35:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_65": {"op": "CONV2D", "inputs": ["@uid_78:out0", "@data_input_uid_310:out0", "@data_input_uid_311:out0"], "inut_shape": [[32, 16, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_37": {"op": "MULTIPLY", "inputs": ["@uid_45:out0", "@uid_46:out0"], "inut_shape": [[256, 128, 1, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_221": {"op": "CONV2D", "inputs": ["@uid_220:out0", "@data_input_uid_312:out0", "@data_input_uid_313:out0"], "inut_shape": [[256, 128, 128, 1], [1, 1, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_50": {"op": "RELU", "inputs": ["@uid_65:out0"], "inut_shape": [[32, 16, 256, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 256, 1]]}, "uid_31": {"op": "ADD", "inputs": ["@uid_37:out0", "@uid_38:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_16": {"op": "SIGMOID", "inputs": ["@uid_221:out0"], "inut_shape": [[256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_40": {"op": "CONV2D", "inputs": ["@uid_50:out0", "@data_input_uid_314:out0", "@data_input_uid_315:out0"], "inut_shape": [[32, 16, 256, 1], [1, 1, 256, 512], [512]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_25": {"op": "RELU", "inputs": ["@uid_31:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_15": {"op": "SUBTRACT", "inputs": ["@uid_22:out0", "@uid_16:out0"], "inut_shape": [[1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_33": {"op": "ADD", "inputs": ["@uid_40:out0", "@uid_41:out0"], "inut_shape": [[32, 16, 512, 1], [32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_44": {"op": "CONV2D", "inputs": ["@uid_25:out0", "@data_input_uid_316:out0", "@data_input_uid_317:out0"], "inut_shape": [[256, 128, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_27": {"op": "BATCH_NORM", "inputs": ["@uid_33:out0", "@data_input_uid_318:out0", "@data_input_uid_319:out0", "@data_input_uid_320:out0", "@data_input_uid_321:out0"], "inut_shape": [[32, 16, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_90": {"op": "BATCH_NORM", "inputs": ["@uid_33:out0", "@data_input_uid_322:out0", "@data_input_uid_323:out0", "@data_input_uid_324:out0", "@data_input_uid_325:out0"], "inut_shape": [[32, 16, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_158": {"op": "POOL", "inputs": ["@uid_33:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[1, 1, 512, 1]]}, "uid_169": {"op": "POOL", "inputs": ["@uid_33:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[16, 8, 512, 1]]}, "uid_170": {"op": "POOL", "inputs": ["@uid_33:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[8, 4, 512, 1]]}, "uid_171": {"op": "POOL", "inputs": ["@uid_33:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[4, 2, 512, 1]]}, "uid_36": {"op": "RELU", "inputs": ["@uid_44:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_21": {"op": "RELU", "inputs": ["@uid_27:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_76": {"op": "RELU", "inputs": ["@uid_90:out0"], "inut_shape": [[32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 512, 1]]}, "uid_143": {"op": "BATCH_NORM", "inputs": ["@uid_158:out0", "@data_input_uid_326:out0", "@data_input_uid_327:out0", "@data_input_uid_328:out0", "@data_input_uid_329:out0"], "inut_shape": [[1, 1, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[1, 1, 512, 1]]}, "uid_155": {"op": "BATCH_NORM", "inputs": ["@uid_169:out0", "@data_input_uid_330:out0", "@data_input_uid_331:out0", "@data_input_uid_332:out0", "@data_input_uid_333:out0"], "inut_shape": [[16, 8, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[16, 8, 512, 1]]}, "uid_156": {"op": "BATCH_NORM", "inputs": ["@uid_170:out0", "@data_input_uid_334:out0", "@data_input_uid_335:out0", "@data_input_uid_336:out0", "@data_input_uid_337:out0"], "inut_shape": [[8, 4, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[8, 4, 512, 1]]}, "uid_157": {"op": "BATCH_NORM", "inputs": ["@uid_171:out0", "@data_input_uid_338:out0", "@data_input_uid_339:out0", "@data_input_uid_340:out0", "@data_input_uid_341:out0"], "inut_shape": [[4, 2, 512, 1], [512], [512], [512], [512]], "outputs": ["out0"], "output_shape": [[4, 2, 512, 1]]}, "uid_30": {"op": "CONV2D", "inputs": ["@uid_36:out0", "@data_input_uid_342:out0", "@data_input_uid_343:out0"], "inut_shape": [[256, 128, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_63": {"op": "CONV2D", "inputs": ["@uid_76:out0", "@data_input_uid_344:out0", "@data_input_uid_345:out0"], "inut_shape": [[32, 16, 512, 1], [1, 1, 512, 96], [96]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_142": {"op": "RELU", "inputs": ["@uid_143:out0"], "inut_shape": [[1, 1, 512, 1]], "outputs": ["out0"], "output_shape": [[1, 1, 512, 1]]}, "uid_139": {"op": "RELU", "inputs": ["@uid_155:out0"], "inut_shape": [[16, 8, 512, 1]], "outputs": ["out0"], "output_shape": [[16, 8, 512, 1]]}, "uid_140": {"op": "RELU", "inputs": ["@uid_156:out0"], "inut_shape": [[8, 4, 512, 1]], "outputs": ["out0"], "output_shape": [[8, 4, 512, 1]]}, "uid_141": {"op": "RELU", "inputs": ["@uid_157:out0"], "inut_shape": [[4, 2, 512, 1]], "outputs": ["out0"], "output_shape": [[4, 2, 512, 1]]}, "uid_24": {"op": "RELU", "inputs": ["@uid_30:out0"], "inut_shape": [[256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 64, 1]]}, "uid_125": {"op": "CONV2D", "inputs": ["@uid_142:out0", "@data_input_uid_346:out0", "@data_input_uid_347:out0"], "inut_shape": [[1, 1, 512, 1], [1, 1, 512, 96], [96]], "outputs": ["out0"], "output_shape": [[1, 1, 96, 1]]}, "uid_119": {"op": "CONV2D", "inputs": ["@uid_139:out0", "@data_input_uid_348:out0", "@data_input_uid_349:out0"], "inut_shape": [[16, 8, 512, 1], [1, 1, 512, 96], [96]], "outputs": ["out0"], "output_shape": [[16, 8, 96, 1]]}, "uid_121": {"op": "CONV2D", "inputs": ["@uid_140:out0", "@data_input_uid_350:out0", "@data_input_uid_351:out0"], "inut_shape": [[8, 4, 512, 1], [1, 1, 512, 96], [96]], "outputs": ["out0"], "output_shape": [[8, 4, 96, 1]]}, "uid_123": {"op": "CONV2D", "inputs": ["@uid_141:out0", "@data_input_uid_352:out0", "@data_input_uid_353:out0"], "inut_shape": [[4, 2, 512, 1], [1, 1, 512, 96], [96]], "outputs": ["out0"], "output_shape": [[4, 2, 96, 1]]}, "uid_216": {"op": "CONCAT", "inputs": ["@uid_24:out0", "@uid_25:out0"], "inut_shape": [[256, 128, 64, 1], [256, 128, 64, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_124": {"op": "PAD2", "inputs": ["@uid_125:out0"], "inut_shape": [[1, 1, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_118": {"op": "RESIZE", "inputs": ["@uid_119:out0"], "inut_shape": [[16, 8, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_120": {"op": "RESIZE", "inputs": ["@uid_121:out0"], "inut_shape": [[8, 4, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_122": {"op": "RESIZE", "inputs": ["@uid_123:out0"], "inut_shape": [[4, 2, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_217": {"op": "CONV2D", "inputs": ["@uid_216:out0", "@data_input_uid_354:out0", "@data_input_uid_355:out0"], "inut_shape": [[256, 128, 128, 1], [1, 1, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_107": {"op": "ADD", "inputs": ["@uid_124:out0", "@uid_63:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_104": {"op": "ADD", "inputs": ["@uid_118:out0", "@uid_63:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_105": {"op": "ADD", "inputs": ["@uid_120:out0", "@uid_63:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_106": {"op": "ADD", "inputs": ["@uid_122:out0", "@uid_63:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 96, 1]]}, "uid_14": {"op": "MULTIPLY", "inputs": ["@uid_16:out0", "@uid_217:out0"], "inut_shape": [[256, 128, 128, 1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_93": {"op": "CONCAT", "inputs": ["@uid_104:out0", "@uid_105:out0", "@uid_106:out0", "@uid_107:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 96, 1], [32, 16, 96, 1], [32, 16, 96, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 384, 1]]}, "uid_91": {"op": "BATCH_NORM", "inputs": ["@uid_93:out0", "@data_input_uid_356:out0", "@data_input_uid_357:out0", "@data_input_uid_358:out0", "@data_input_uid_359:out0"], "inut_shape": [[32, 16, 384, 1], [384], [384], [384], [384]], "outputs": ["out0"], "output_shape": [[32, 16, 384, 1]]}, "uid_77": {"op": "RELU", "inputs": ["@uid_91:out0"], "inut_shape": [[32, 16, 384, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 384, 1]]}, "uid_64": {"op": "GROUPED_CONV2D", "inputs": ["@uid_77:out0", "@data_input_uid_360:out0", "@data_input_uid_361:out0"], "inut_shape": [[32, 16, 384, 1], [3, 3, 96, 384], [384]], "outputs": ["out0"], "output_shape": [[32, 16, 384, 1]]}, "uid_49": {"op": "CONCAT", "inputs": ["@uid_63:out0", "@uid_64:out0"], "inut_shape": [[32, 16, 96, 1], [32, 16, 384, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 480, 1]]}, "uid_39": {"op": "BATCH_NORM", "inputs": ["@uid_49:out0", "@data_input_uid_362:out0", "@data_input_uid_363:out0", "@data_input_uid_364:out0", "@data_input_uid_365:out0"], "inut_shape": [[32, 16, 480, 1], [480], [480], [480], [480]], "outputs": ["out0"], "output_shape": [[32, 16, 480, 1]]}, "uid_32": {"op": "RELU", "inputs": ["@uid_39:out0"], "inut_shape": [[32, 16, 480, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 480, 1]]}, "uid_218": {"op": "CONCAT", "inputs": ["@uid_32:out0", "@uid_21:out0"], "inut_shape": [[32, 16, 480, 1], [32, 16, 512, 1]], "outputs": ["out0"], "output_shape": [[32, 16, 992, 1]]}, "uid_219": {"op": "CONV2D", "inputs": ["@uid_218:out0", "@data_input_uid_366:out0", "@data_input_uid_367:out0"], "inut_shape": [[32, 16, 992, 1], [1, 1, 992, 128], [128]], "outputs": ["out0"], "output_shape": [[32, 16, 128, 1]]}, "uid_13": {"op": "RESIZE", "inputs": ["@uid_219:out0"], "inut_shape": [[32, 16, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_10": {"op": "ADD", "inputs": ["@uid_13:out0", "@uid_14:out0"], "inut_shape": [[256, 128, 128, 1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_11": {"op": "MULTIPLY", "inputs": ["@uid_15:out0", "@uid_13:out0"], "inut_shape": [[256, 128, 128, 1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_9": {"op": "ADD", "inputs": ["@uid_11:out0", "@uid_217:out0"], "inut_shape": [[256, 128, 128, 1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_214": {"op": "CONCAT", "inputs": ["@uid_9:out0", "@uid_10:out0"], "inut_shape": [[256, 128, 128, 1], [256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 256, 1]]}, "uid_215": {"op": "CONV2D", "inputs": ["@uid_214:out0", "@data_input_uid_368:out0", "@data_input_uid_369:out0"], "inut_shape": [[256, 128, 256, 1], [1, 1, 256, 128], [128]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_5": {"op": "BATCH_NORM", "inputs": ["@uid_215:out0", "@data_input_uid_370:out0", "@data_input_uid_371:out0", "@data_input_uid_372:out0", "@data_input_uid_373:out0"], "inut_shape": [[256, 128, 128, 1], [128], [128], [128], [128]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_4": {"op": "RELU", "inputs": ["@uid_5:out0"], "inut_shape": [[256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_3": {"op": "CONV2D", "inputs": ["@uid_4:out0", "@data_input_uid_374:out0", "@data_input_uid_375:out0"], "inut_shape": [[256, 128, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_2": {"op": "RELU", "inputs": ["@uid_3:out0"], "inut_shape": [[256, 128, 128, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 128, 1]]}, "uid_1": {"op": "CONV2D", "inputs": ["@uid_2:out0", "@data_input_uid_376:out0", "@data_input_uid_377:out0"], "inut_shape": [[256, 128, 128, 1], [1, 1, 128, 19], [19]], "outputs": ["out0"], "output_shape": [[256, 128, 19, 1]]}, "uid_20000": {"op": "POST_PROCESS", "inputs": ["@uid_1:out0"], "inut_shape": [[256, 128, 19, 1]], "outputs": ["out0"], "output_shape": [[256, 128, 19, 1]]}, "uid_30000": {"op": "DATACONVERT", "inputs": ["@data_input_uid_378:out0"], "inut_shape": [[2048, 1024, 3, 1]], "outputs": ["out0"], "output_shape": [[2048, 1024, 3, 1]]}, "data_input_uid_201": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_202": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_203": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_204": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_205": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_206": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_207": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_208": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_209": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_210": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_211": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_212": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_213": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_214": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_215": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_216": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_217": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_218": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_219": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_220": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_221": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_222": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_223": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_224": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_225": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_226": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_227": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_228": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_229": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_230": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_231": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_232": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_233": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_234": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_235": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_236": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_237": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_238": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_239": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_240": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_241": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_242": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_243": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_244": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_245": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_246": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_247": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_248": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_249": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_250": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_251": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_252": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_253": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_254": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_255": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_256": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_257": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_258": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_259": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_260": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_261": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_262": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_263": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_264": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_265": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_266": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_267": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_268": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_269": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_270": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_271": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_272": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_273": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_274": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_275": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_276": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_277": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_278": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_279": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_280": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_281": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_282": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_283": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_284": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_285": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_286": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_287": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_288": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_289": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_290": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_291": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_292": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_293": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_294": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_295": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_296": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_297": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_298": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_299": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_300": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_301": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_302": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_303": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_304": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_305": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_306": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_307": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_308": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_309": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_310": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_311": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_312": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_313": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_314": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_315": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_316": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_317": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_318": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_319": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_320": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_321": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_322": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_323": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_324": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_325": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_326": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_327": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_328": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_329": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_330": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_331": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_332": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_333": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_334": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_335": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_336": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_337": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_338": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_339": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_340": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_341": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_342": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_343": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_344": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_345": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_346": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_347": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_348": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_349": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_350": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_351": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_352": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_353": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_354": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_355": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_356": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_357": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_358": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_359": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_360": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_361": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_362": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_363": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_364": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_365": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_366": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_367": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_368": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_369": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_370": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_371": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_372": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_373": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_374": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_375": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_376": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_377": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_378": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}}}
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" cleanCommand="rm -rf" description="" errorParsers="org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********" name="Debug" parent="cdt.managedbuild.config.gnu.linux.openvx.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="cdt.managedbuild.toolchain.gnu.linux.openvx.exe.debug.1704600254" name="OpenVX GCC(Linux)" superClass="cdt.managedbuild.toolchain.gnu.linux.openvx.exe.debug">
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.linux.openvx.platform.exe.debug.1602133548" name="Debug Platform" superClass="cdt.managedbuild.target.gnu.linux.openvx.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/${project_name}/Debug}" errorParsers="" id="cdt.managedbuild.target.gnu.linux.openvx.builder.exe.debug.349572263" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="CDT Internal Builder" superClass="cdt.managedbuild.target.gnu.linux.openvx.builder.exe.debug"/>
							<tool command="as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.assembler.exe.debug.1219759698" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.assembler.exe.debug">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.448743315" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
							<tool command="gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.debug.**********" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.linux.openvx.c.compiler.exe.debug.option.optimization.level.859665887" name="Optimization Level" superClass="gnu.linux.openvx.c.compiler.exe.debug.option.optimization.level" valueType="enumerated"/>
								<option id="gnu.linux.openvx.c.compiler.exe.debug.option.debugging.level.1326095069" name="Debug Level" superClass="gnu.linux.openvx.c.compiler.exe.debug.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.1651764214" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${VIVANTE_SDK_DIR}/include/ovxlib&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.502494195" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool command="g++" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.debug.**********" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.debug">
								<option id="gnu.linux.openvx.cpp.compiler.exe.debug.option.optimization.level.382229710" name="Optimization Level" superClass="gnu.linux.openvx.cpp.compiler.exe.debug.option.optimization.level" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.linux.openvx.cpp.compiler.exe.debug.option.debugging.level.943151929" name="Debug Level" superClass="gnu.linux.openvx.cpp.compiler.exe.debug.option.debugging.level" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.include.paths.1241742902" name="Include paths (-I)" superClass="gnu.cpp.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${VIVANTE_SDK_DIR}/include/ovxlib&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.301472061" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.c.linker.exe.debug.634321620" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.c.linker.exe.debug"/>
							<tool command="g++" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.cpp.linker.exe.debug.520234194" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.cpp.linker.exe.debug">
								<option id="gnu.cpp.link.option.libs.1847698426" name="Libraries (-l)" superClass="gnu.cpp.link.option.libs" valueType="libs">
									<listOptionValue builtIn="false" value="ovxlib"/>
									<listOptionValue builtIn="false" value="OpenVX"/>
									<listOptionValue builtIn="false" value="OpenVXU"/>
									<listOptionValue builtIn="false" value="jpeg"/>
									<listOptionValue builtIn="false" value="m"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.1790836754" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.archiver.openvx.1346749068" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.linux.archiver.openvx"/>
							<tool command="vcCompiler" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.compiler.gcpgm.debug.1785186538" name="OpenVX Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.compiler.gcpgm.debug">
								<option defaultValue="vip.vc.compiler.option.optimization.level.none" id="vip.vc.compiler.option.optimization.level.713126919" name="Optimization Level" superClass="vip.vc.compiler.option.optimization.level" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.vip.vc.compiler.input.vx.3013383" superClass="cdt.managedbuild.tool.vip.vc.compiler.input.vx"/>
								<inputType id="cdt.managedbuild.tool.vip.vc.compiler.input.cl.1350612027" superClass="cdt.managedbuild.tool.vip.vc.compiler.input.cl"/>
							</tool>
							<tool command="vcLinker" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="cdt.managedbuild.tool.gnu.linux.openvx.linker.gcpgm.debug.176361644" name="OpenVX Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.linker.gcpgm.debug"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
			<storageModule moduleId="scannerConfiguration">
				<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
				<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="makefileGenerator">
						<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329;cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329.;cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********;cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********.;cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.debug.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.301472061">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329;cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329.;cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.856432265">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********;cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********.;cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.debug.**********;cdt.managedbuild.tool.gnu.c.compiler.input.502494195">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release,org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" cleanCommand="rm -rf" description="" id="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329" name="Release" parent="cdt.managedbuild.config.gnu.linux.openvx.exe.release">
					<folderInfo id="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.linux.openvx.exe.release.1587127856" name="OpenVX GCC(Linux)" superClass="cdt.managedbuild.toolchain.gnu.linux.openvx.exe.release">
							<targetPlatform id="cdt.managedbuild.target.gnu.linux.openvx.platform.exe.release.289463889" name="Debug Platform" superClass="cdt.managedbuild.target.gnu.linux.openvx.platform.exe.release"/>
							<builder buildPath="${workspace_loc:/${project_name}/Release}" id="cdt.managedbuild.target.gnu.linux.openvx.builder.exe.release.1184232161" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="CDT Internal Builder" superClass="cdt.managedbuild.target.gnu.linux.openvx.builder.exe.release"/>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.assembler.exe.release.355734481" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.assembler.exe.release">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.368016013" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.release.**********" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.release">
								<option defaultValue="gnu.c.optimization.level.most" id="gnu.linux.openvx.c.compiler.exe.release.option.optimization.level.1553939765" name="Optimization Level" superClass="gnu.linux.openvx.c.compiler.exe.release.option.optimization.level" valueType="enumerated"/>
								<option id="gnu.linux.openvx.c.compiler.exe.release.option.debugging.level.1980676931" name="Debug Level" superClass="gnu.linux.openvx.c.compiler.exe.release.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.1031613412" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${VIVANTE_SDK_DIR}/include/ovxlib&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.release.**********" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.release">
								<option id="gnu.linux.openvx.cpp.compiler.exe.release.option.optimization.level.734580791" name="Optimization Level" superClass="gnu.linux.openvx.cpp.compiler.exe.release.option.optimization.level" value="gnu.cpp.compiler.optimization.level.most" valueType="enumerated"/>
								<option id="gnu.linux.openvx.cpp.compiler.exe.release.option.debugging.level.239734134" name="Debug Level" superClass="gnu.linux.openvx.cpp.compiler.exe.release.option.debugging.level" value="gnu.cpp.compiler.debugging.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.include.paths.1384073886" name="Include paths (-I)" superClass="gnu.cpp.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${VIVANTE_SDK_DIR}/include/ovxlib&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.856432265" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.c.linker.exe.release.1176963850" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.c.linker.exe.release"/>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.cpp.linker.exe.release.413519374" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.cpp.linker.exe.release">
								<option id="gnu.cpp.link.option.libs.1661239344" name="Libraries (-l)" superClass="gnu.cpp.link.option.libs" valueType="libs">
									<listOptionValue builtIn="false" value="ovxlib"/>
									<listOptionValue builtIn="false" value="OpenVX"/>
									<listOptionValue builtIn="false" value="OpenVXU"/>
									<listOptionValue builtIn="false" value="jpeg"/>
									<listOptionValue builtIn="false" value="m"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.25009685" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.archiver.openvx.1506044250" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.linux.archiver.openvx"/>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.compiler.gcpgm.release.862099167" name="OpenVX Compiler" superClass="cdt.managedbuild.tool.gnu.linux.openvx.compiler.gcpgm.release">
								<option defaultValue="vip.vc.compiler.option.optimization.level.none" id="vip.vc.compiler.option.optimization.level.778061367" name="Optimization Level" superClass="vip.vc.compiler.option.optimization.level" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.vip.vc.compiler.input.vx.1221562505" superClass="cdt.managedbuild.tool.vip.vc.compiler.input.vx"/>
								<inputType id="cdt.managedbuild.tool.vip.vc.compiler.input.cl.210232097" superClass="cdt.managedbuild.tool.vip.vc.compiler.input.cl"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.linux.openvx.linker.gcpgm.release.1180341267" name="OpenVX Linker" superClass="cdt.managedbuild.tool.gnu.linux.openvx.linker.gcpgm.release"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
			<storageModule moduleId="scannerConfiguration">
				<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
				<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="makefileGenerator">
						<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
					<buildOutputProvider>
						<openAction enabled="true" filePath=""/>
						<parser enabled="true"/>
					</buildOutputProvider>
					<scannerInfoProvider id="specsFile">
						<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
						<parser enabled="true"/>
					</scannerInfoProvider>
				</profile>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329;cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329.;cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********;cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********.;cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.debug.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.301472061">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329;cdt.managedbuild.config.gnu.linux.openvx.exe.release.607456329.;cdt.managedbuild.tool.gnu.linux.openvx.cpp.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.856432265">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
				<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********;cdt.managedbuild.config.gnu.linux.openvx.exe.debug.**********.;cdt.managedbuild.tool.gnu.linux.openvx.c.compiler.exe.debug.**********;cdt.managedbuild.tool.gnu.c.compiler.input.502494195">
					<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="makefileGenerator">
							<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
					<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
						<buildOutputProvider>
							<openAction enabled="true" filePath=""/>
							<parser enabled="true"/>
						</buildOutputProvider>
						<scannerInfoProvider id="specsFile">
							<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
							<parser enabled="true"/>
						</scannerInfoProvider>
					</profile>
				</scannerConfigBuildInfo>
			</storageModule>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="${project_name}.cdt.managedbuild.target.gnu.linux.openvx.exe.278286742" name="Executable" projectType="cdt.managedbuild.target.gnu.linux.openvx.exe"/>
	</storageModule>
</cproject>

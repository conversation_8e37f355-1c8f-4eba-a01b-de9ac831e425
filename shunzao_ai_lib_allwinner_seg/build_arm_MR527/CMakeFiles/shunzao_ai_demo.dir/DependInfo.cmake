# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/main.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527/CMakeFiles/shunzao_ai_demo.dir/main.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_GLIBCXX_USE_CXX11_ABI=1"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../../common/include"
  "../../common"
  "../include"
  "../include/algorithm"
  "../include/utils"
  "../include/basic_model"
  "../include/task"
  "/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")

#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../common/include/vip_lite.h
vip_lite_common.h
../../common/include/vip_lite_common.h

../../common/include/vip_lite_common.h

../../common/npulib.h
stdint.h
-
cstddef
-

../include/algorithm/line_decode.h
opencv2/core/core.hpp
-
data_type.h
../include/algorithm/data_type.h

../include/algorithm/yolov5.h
opencv2/core/core.hpp
-

../include/algorithm/yolov8.h
opencv2/core/core.hpp
-
data_type.h
../include/algorithm/data_type.h

../include/basic_model/base_model.h
data_type.h
../include/basic_model/data_type.h
npulib.h
../include/basic_model/npulib.h

../include/data_type.h
vector
-
iostream
-
cstdio
-
string
-
opencv2/core/core.hpp
../include/opencv2/core/core.hpp
opencv2/highgui/highgui.hpp
../include/opencv2/highgui/highgui.hpp
opencv2/imgproc.hpp
../include/opencv2/imgproc.hpp

../include/shunzao_ai_lib.h
data_type.h
../include/data_type.h
opencv2/core/core.hpp
../include/opencv2/core/core.hpp
opencv2/highgui/highgui.hpp
../include/opencv2/highgui/highgui.hpp
opencv2/imgproc.hpp
../include/opencv2/imgproc.hpp

../include/shunzao_ai_task.h
string
-
vector
-
map
-
iostream
-
npulib.h
../include/npulib.h
base_model.h
../include/base_model.h
shunzao_ai_lib.h
../include/shunzao_ai_lib.h
ground_det.h
../include/ground_det.h
line_det.h
../include/line_det.h
slam_det.h
../include/slam_det.h
slam_seg.h
../include/slam_seg.h

../include/task/ground_det.h
string
-
npulib.h
-
base_model.h
../include/task/base_model.h
npulib.h
../include/task/npulib.h
yolov8.h
../include/task/yolov8.h
data_type.h
../include/task/data_type.h

../include/task/line_det.h
string
-
npulib.h
-
base_model.h
../include/task/base_model.h
npulib.h
../include/task/npulib.h
line_decode.h
../include/task/line_decode.h
data_type.h
../include/task/data_type.h

../include/task/slam_det.h
string
-
npulib.h
-
base_model.h
../include/task/base_model.h
npulib.h
../include/task/npulib.h
yolov8.h
../include/task/yolov8.h
data_type.h
../include/task/data_type.h

../include/task/slam_seg.h
iostream
-
vector
-
string
-
opencv2/opencv.hpp
-
sys/time.h
-
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-
data_type.h
../include/task/data_type.h
base_model.h
../include/task/base_model.h
shunzao_ai_lib.h
../include/task/shunzao_ai_lib.h

../include/utils/utils.h
fstream
-
iostream
-
iterator
-
string.h
-
vector
-
cmath
-
sys/time.h
-
opencv2/core/core.hpp
../include/utils/opencv2/core/core.hpp
opencv2/highgui/highgui.hpp
../include/utils/opencv2/highgui/highgui.hpp
opencv2/imgproc.hpp
../include/utils/opencv2/imgproc.hpp
unordered_map
-
data_type.h
../include/utils/data_type.h

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/cvdef.h
opencv2/core/base.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/base.hpp
opencv2/core/cvstd.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/cvstd.hpp
opencv2/core/traits.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/traits.hpp
opencv2/core/matx.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/matx.hpp
opencv2/core/types.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/types.hpp
opencv2/core/mat.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/mat.hpp
opencv2/core/persistence.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/persistence.hpp
opencv2/core/operations.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/operations.hpp
opencv2/core/cvstd.inl.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/cvstd.inl.hpp
opencv2/core/utility.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/utility.hpp
opencv2/core/optim.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/optim.hpp
opencv2/core/ovx.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core/ovx.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/async.hpp
opencv2/core/mat.hpp
-
chrono
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/base.hpp
opencv2/opencv_modules.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/opencv_modules.hpp
climits
-
algorithm
-
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/neon_utils.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/neon_utils.hpp
opencv2/core/vsx_utils.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/vsx_utils.hpp
opencv2/core/check.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/check.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/bufferpool.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/check.hpp
opencv2/core/base.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/core.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_dispatch.h
cv_cpu_config.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_config.h
cv_cpu_helper.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_helper.h
emmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
nmmintrin.h
-
popcntintrin.h
-
immintrin.h
-
arm_neon.h
-
immintrin.h
-
arm_neon.h
-
immintrin.h
-
immintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
riscv-vector.h
-
arm_neon.h
-
altivec.h
-
hal/msa_macros.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/msa_macros.h
lsxintrin.h
-
lasxintrin.h
-
wasm_simd128.h
-
riscv_vector.h
-
emmintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-
immintrin.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_helper.h

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvdef.h
opencv2/core/version.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/version.hpp
cvconfig.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvconfig.h
limits
-
limits.h
-
opencv2/core/hal/interface.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/hal/interface.h
cv_cpu_dispatch.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_dispatch.h
intrin.h
-
array
-
cstdint
-
stdint.h
-
stdint.h
-
opencv2/core/fast_math.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/fast_math.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
cstddef
-
cstring
-
cctype
-
string
-
algorithm
-
utility
-
cstdlib
-
cmath
-
cvstd_wrapper.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd_wrapper.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd.inl.hpp
complex
-
ostream
-
sstream
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd_wrapper.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
string
-
memory
-
type_traits
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/fast_math.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
cmath
-
fastmath.h
-
math.h
-
emmintrin.h
-
altivec.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/interface.h
cstddef
-
stddef.h
-
stdbool.h
-
cstdint
-
stdint.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/msa_macros.h
msa.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/msa.h
stdint.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/mat.hpp
opencv2/core/matx.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/matx.hpp
opencv2/core/types.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/types.hpp
opencv2/core/bufferpool.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/bufferpool.hpp
type_traits
-
opencv2/core/mat.inl.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/mat.inl.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/mat.inl.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/matx.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/base.hpp
opencv2/core/traits.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/traits.hpp
opencv2/core/saturate.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/saturate.hpp
initializer_list
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/neon_utils.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/operations.hpp
cstdio
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/optim.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/ovx.hpp
cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvdef.h

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/persistence.hpp
opencv2/core/types.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/types.hpp
opencv2/core/mat.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/mat.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core.hpp
time.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/saturate.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
climits
-
opencv2/core/fast_math.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/fast_math.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/traits.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/types.hpp
climits
-
cfloat
-
vector
-
limits
-
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/matx.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/matx.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utility.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core.hpp
ostream
-
functional
-
mutex
-
opencv2/core/utils/instrumentation.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/utils/instrumentation.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utils/instrumentation.hpp
opencv2/core/utility.hpp
-
opencv2/core/utils/tls.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utils/tls.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/version.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/vsx_utils.hpp
opencv2/core/cvdef.h
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/opencv2/core/cvdef.h
assert.h
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn.hpp
opencv2/dnn/dnn.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dict.hpp
opencv2/core.hpp
-
map
-
ostream
-
opencv2/dnn/dnn.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dnn.hpp
vector
-
opencv2/core.hpp
-
opencv2/core/async.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/opencv2/core/async.hpp
../dnn/version.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/version.hpp
opencv2/dnn/dict.hpp
-
opencv2/dnn/layer.hpp
-
opencv2/dnn/dnn.inl.hpp
-
opencv2/dnn/utils/inference_engine.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dnn.inl.hpp
opencv2/dnn.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/layer.hpp
opencv2/dnn.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/utils/inference_engine.hpp
../dnn.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dnn.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/version.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/features2d.hpp
opencv2/opencv_modules.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/opencv_modules.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
opencv2/flann/miniflann.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/flann/miniflann.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/highgui.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
opencv2/imgcodecs.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/imgcodecs.hpp
opencv2/videoio.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/videoio.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/highgui/highgui.hpp
opencv2/highgui.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/highgui/opencv2/highgui.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgcodecs.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
./imgproc/segmentation.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/segmentation.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/imgproc.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/opencv2/imgproc.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/segmentation.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/opencv2/imgproc.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/ml.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
float.h
-
map
-
iostream
-
opencv2/ml/ml.inl.hpp
-

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/ml/ml.inl.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv.hpp
opencv2/opencv_modules.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/opencv_modules.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
opencv2/calib3d.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/calib3d.hpp
opencv2/features2d.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/features2d.hpp
opencv2/dnn.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/dnn.hpp
opencv2/flann.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/flann.hpp
opencv2/highgui.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/highgui.hpp
opencv2/imgcodecs.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/imgcodecs.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/imgproc.hpp
opencv2/ml.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/ml.hpp
opencv2/objdetect.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/objdetect.hpp
opencv2/photo.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/photo.hpp
opencv2/stitching.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/stitching.hpp
opencv2/video.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/video.hpp
opencv2/videoio.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/videoio.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv_modules.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/photo.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/imgproc.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video.hpp
opencv2/video/tracking.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/video/tracking.hpp
opencv2/video/background_segm.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/video/background_segm.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/background_segm.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/opencv2/core.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/tracking.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/opencv2/core.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/opencv2/imgproc.hpp

/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/videoio.hpp
opencv2/core.hpp
/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv2/core.hpp

/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp
vip_lite.h
-
stdio.h
-
stdint.h
-
memory.h
-
stdlib.h
-
string.h
-
npu_util.h
/root/pan/shunzao_ai_lib-develop/common/npu_util.h

/root/pan/shunzao_ai_lib-develop/common/npu_util.h
stdint.h
-

/root/pan/shunzao_ai_lib-develop/common/npulib.cpp
vip_lite.h
-
stdio.h
-
stdint.h
-
memory.h
-
stdlib.h
-
string.h
-
sys/time.h
-
npu_util.h
/root/pan/shunzao_ai_lib-develop/common/npu_util.h
npulib.h
/root/pan/shunzao_ai_lib-develop/common/npulib.h

/root/pan/shunzao_ai_lib-develop/common/npulib.h
stdint.h
-
cstddef
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/line_decode.cc
line_decode.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/line_decode.h
opencv2/opencv.hpp
-
opencv2/imgproc/imgproc.hpp
-
opencv2/highgui/highgui.hpp
-
iomanip
-
iostream
-
vector
-
algorithm
-
cmath
-
unordered_map
-
utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/utils.h
data_type.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/data_type.h

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov5.cc
opencv2/core/core.hpp
-
opencv2/highgui/highgui.hpp
-
opencv2/imgproc/imgproc.hpp
-
iostream
-
stdio.h
-
vector
-
cmath
-
yolov5.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov5.h

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov5s_pre.cpp
opencv2/core/core.hpp
-
opencv2/highgui/highgui.hpp
-
opencv2/imgproc/imgproc.hpp
-
iostream
-
stdio.h
-
stdint.h
-
string.h
-
math.h
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov8.cc
yolov8.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/yolov8.h
opencv2/opencv.hpp
-
opencv2/imgproc/imgproc.hpp
-
opencv2/highgui/highgui.hpp
-
iomanip
-
iostream
-
vector
-
algorithm
-
cmath
-
unordered_map
-
utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/utils.h
data_type.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/algorithm/data_type.h

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/basic_model/base_model.cc
npulib.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/basic_model/npulib.h
iostream
-
cstdlib
-
base_model.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/basic_model/base_model.h
opencv2/opencv.hpp
-
opencv2/core/core.hpp
-
opencv2/imgproc/imgproc.hpp
-
opencv2/highgui/highgui.hpp
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/shunzao_ai_task.cc
sys/time.h
-
utils/utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/utils/utils.h
shunzao_ai_task.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/shunzao_ai_task.h
npulib.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/npulib.h
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-
opencv2/core/core.hpp
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/opencv2/core/core.hpp
opencv2/highgui/highgui.hpp
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/opencv2/highgui/highgui.hpp
opencv2/imgproc.hpp
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/opencv2/imgproc.hpp
thread
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/ground_det.cc
ground_det.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/ground_det.h
utils/utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/utils/utils.h
shunzao_ai_task.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_task.h
shunzao_ai_lib.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_lib.h
yolov5.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/yolov5.h
yolov8.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/yolov8.h
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-
chrono
-
chrono
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/line_det.cc
line_det.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/line_det.h
data_type.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/data_type.h
line_decode.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/line_decode.h
utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/utils.h
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-
shunzao_ai_task.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_task.h

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_det.cc
slam_det.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_det.h
utils/utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/utils/utils.h
shunzao_ai_task.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_task.h
shunzao_ai_lib.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_lib.h
yolov5.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/yolov5.h
yolov8.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/yolov8.h
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-
chrono
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_seg.cc
slam_seg.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/slam_seg.h
utils/utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/utils/utils.h
shunzao_ai_task.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_task.h
shunzao_ai_lib.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/task/shunzao_ai_lib.h
rapidjson/document.h
-
rapidjson/stringbuffer.h
-
rapidjson/writer.h
-

/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/utils/utils.cc
utils/utils.h
/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner_seg/src/utils/utils/utils.h
opencv2/opencv.hpp
-
opencv2/core/core.hpp
-
opencv2/imgproc/imgproc.hpp
-
opencv2/highgui/highgui.hpp
-
iomanip
-
fstream
-


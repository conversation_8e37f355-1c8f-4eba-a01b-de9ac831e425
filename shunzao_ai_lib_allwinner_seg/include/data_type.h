/**
 * @Autor: yaoshi,
 * @time: 2025-05-07,
 * @description: 顺造AI模型库，定义数据结构
 */
#ifndef DATA_TYPE_H_
#define DATA_TYPE_H_
#include <vector>
#include <iostream>
#include <cstdio>
#include <string>
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc.hpp"
typedef struct BBox 
{
    float xmin;
    float ymin;
    float xmax;
    float ymax;
    int label;
    float score;
} BBox;

typedef struct BBoxFlag
{
    BBox box;
    float flag; //测距标志位
}BBoxFlag;



struct BaseData {
  /// type
  std::string type_ = "";
  /// name
  std::string name_ = "";
  /// error code
  int error_code_ = 0;
  /// error detail info
  std::string error_detail_ = "";
};

typedef struct ai_msg
{
    // uint32_t data_size;
    std::vector<BBoxFlag> bboxes;
    uint32_t model_id;
    // uint32_t core_id;
    uint64_t frameid;
    // uint64_t data;
    // uint64_t msg_num;
    /* data */
}ai_msg_t;

typedef struct ImgData: public BaseData
{
    const char* image_data_addr;
    int32_t image_height;
    int32_t image_width;
    std::string path;
    
    inline ImgData(){type_ = "ImgData";}
    inline ImgData(const char* addr, int32_t imgh, int32_t imgw)
    :image_data_addr(addr), image_height(imgh), image_width(imgw){
        type_ = "ImgData";
    }
    /* data */
}ImgData;

typedef struct ImageBboxes: public ImgData{
    std::vector<BBox> bboxes_;
    ImageBboxes(const char* addr, int32_t h, int32_t w, std::vector<BBox>bboxes)
    :ImgData(addr,h,w), bboxes_(bboxes){

    }
}ImageBboxes;

// 分割掩码数据结构
typedef struct SegMask {
    int width;          // 掩码宽度
    int height;         // 掩码高度
    int num_classes;    // 类别数量
    std::vector<uint8_t> mask_data;  // 掩码数据，每个像素存储类别ID

    SegMask() : width(0), height(0), num_classes(0) {}
    SegMask(int w, int h, int classes) : width(w), height(h), num_classes(classes) {
        mask_data.resize(w * h);
    }
} SegMask;

// 分割结果标志结构
typedef struct SegMaskFlag {
    SegMask mask;
    float confidence;   // 整体置信度
    float flag;         // 标志位
} SegMaskFlag;


typedef struct InputParam{
    int coreid;
    uint64_t frameid;
    int updated;
    int debug;
    float threshold;
}InputParam;



#endif
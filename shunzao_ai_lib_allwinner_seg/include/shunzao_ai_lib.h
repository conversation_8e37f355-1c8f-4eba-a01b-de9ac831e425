/**
 * @Autor: yaoshi,
 * @time: 2025-05-07,
 * @description: 顺造AI模型库，实现对多个模型的后处理
 */

#ifndef SHUNZAO_AI_LIB_LIBRARY_H
#define ZHUNZAO_AI_LIB_LIBRARY_H
#include "data_type.h"
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc.hpp"

typedef int int32_t;
typedef unsigned long uint64_t;

// typedef struct ImageData
// {
//     const char* image_data_addr;
//     int32_t image_height;
//     int32_t image_width;
//     /* data */
// }ImageData;

#ifdef __cplusplus
extern "C" 
{
#endif
    void* shunzao_ai_init_from_config_interface(const char* config);
    void shunzao_ai_get_time(void* p);
/**
 * Init model from file
 * @param[in] model_file_paths: model file should end with bin
 * @param[in] model_id_list: model file's model id, should same size with model_file_paths
 * @param[in] model_num: model file path's num should equal model num
 * @param[in] cameraParam: size equal , first 9 number is cameraMatrix, then 8 number is coeffs1 , then 1 number is ymax, then 2 number are start position
 * @param[in] enable_log: 1:enable log 0:close log
 */
    void* shunzao_ai_init_interface(const char** model_file_paths, const int* model_id_list, int model_num, const float* cameraParam, int enable_log);

/**
 * model run time interface, apply ai funciton, return boxes etc
 * @param[in] image_data: image data after gdc
 * @param[in] core_id: decide which model run on with core, option [0,1]
 * @param[in] result: return ai results, include data addr, modelid and etc.
 * @param[in] result_len: return ai results‘s length.
 * @param[in] fps_list: decide each task runing times.
 * @param[in] fps_list_len: task length.
 * @param[in] frameid: image frame id, should be unique
 * @param[out] return 0 sucess, return -1 failed
 */
    std::vector<BBoxFlag> shunzao_ai_run_interface(void *p, cv::Mat& img, int32_t core_id, int32_t model_id, void* result_addr,  uint64_t frameid);
    std::vector<SegMaskFlag> shunzao_ai_run_segmentation_interface(void *p, cv::Mat& img, int32_t core_id, int32_t model_id, void* result_addr,  uint64_t frameid);
    void shunzao_ai_run_parallel_interface(
        void *p, 
        void *p2,
        cv::Mat& img, 
        int32_t model_id1,
        int32_t model_id2,
        std::vector<BBoxFlag>& result1,
        std::vector<BBoxFlag>& result2);
    /**
 * 
 * 
 * 释放资源
 */
    void shunzao_ai_deinit_interface(void* p);

#ifdef __cplusplus
}
#endif

#endif
/**
 * @Autor: yaoshi,
 * @data: 2025-05-07,
 * @description: 顺造AI模型库，shunzaoAiTask构造和释放模型任务
 */
#ifndef SHUNZAO_AI_TASK_H
#define SHUNZAO_AI_TASK_H

#include <string>
#include <vector>
#include <map>
#include <iostream>
#include "npulib.h"
#include "base_model.h"
#include "shunzao_ai_lib.h"
#include "ground_det.h"
#include "line_det.h"
#include "slam_det.h"
#include "slam_seg.h"

typedef enum ModelId{
    MODEL_GROUND = 1, //地面模型
    MODEL_LINE = 2, //高精度电线
    MODEL_SLAM = 3, //SLAM检测模型
    MODEL_SLAM_SEG = 4, //SLAM分割模型

}ModelId;


class shunzaoAiTask
{
    
public:
    shunzaoAiTask(const char** model_file_paths, const int* model_id_list, int model_num, const float* cameraParam, int enable_log);
    shunzaoAiTask(std::string& config_string);
    ~shunzaoAiTask();
   
    std::vector<BBoxFlag> run(cv::Mat& img, int taskid, InputParam* conf);
    std::vector<SegMaskFlag> run_segmentation(cv::Mat& img, int taskid, InputParam* conf);
    void getAverageTimes(double& avg_pre, double& avg_infer, double& avg_post) {
        std::cout << "debug1: " << total_preprocess_time << std::endl;
        std::cout << "debug2: " << total_inference_time << std::endl;
        std::cout << "debug3: " << total_postprocess_time << std::endl;

        if (run_count == 0) return;
        avg_pre = static_cast<double>(total_preprocess_time) / run_count / 1000.0;   // 毫秒
        avg_infer = static_cast<double>(total_inference_time) / run_count / 1000.0; // 毫秒
        avg_post = static_cast<double>(total_postprocess_time) / run_count / 1000.0; // 毫秒
    }
    
    void setPreprocessTime(long long interval) { total_preprocess_time += interval; }
    void setInferenceTime(long long interval) { total_inference_time += interval; }
    void setPostprocessTime(long long interval) { total_postprocess_time += interval; }
    void setCount(){run_count++;}
private:
    NpuUint npu_uint;

    GroundDet* grounddet_=nullptr;
    LineDet* line_det_=nullptr;
    SlamDet* slam_det_=nullptr;
    SlamSeg* slam_seg_=nullptr;
    // RugSeg* rugseg_=nullptr;

    char* imageData;
    int imgh, imgw;
    long long total_preprocess_time = 0;
    long long total_inference_time = 0;
    long long total_postprocess_time = 0;
    int run_count = 0;
    void create_model(int model_type,const char* model_file);
    void create_model(int model_type, const char* model_file, std::string config);
    // void init(std::vector<std::string> modelfilepaths, std::vector<int> modelid_list);
    // int loadconfig(std::string config_string);
};

#endif


/**
 * @Autor: AI Assistant
 * @time: 2025-08-19
 * @description: SLAM分割任务，基于pidnet_pcq_x527.nb模型
 */

#ifndef SLAM_SEG_H_
#define SLAM_SEG_H_

#include <iostream>
#include <vector>
#include <string>
#include <opencv2/opencv.hpp>
#include <sys/time.h>
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>

#include "data_type.h"
#include "base_model.h"
#include "shunzao_ai_lib.h"

// 前向声明
class shunzaoAiTask;

class SlamSeg: public NetworkBase
{
public:
    SlamSeg(const char* model_path);
    ~SlamSeg();

    // 初始化模型，传入模型路径
    bool setup(const std::string& model_path);

    // 加载输入数据并进行推理和后处理
    bool run_inference(cv::Mat& img, InputParam* inp);

    // 带时间统计的推理
    bool run_inference_with_time(cv::Mat& img, InputParam* inp);

    // 加载输入数据并进行推理和后处理，并且计时
    bool run_inference(cv::Mat& img, InputParam* inp, shunzaoAiTask* outer);

    // 获取分割结果
    std::vector<SegMaskFlag> get_prediction(int* size);

    // 加载配置
    int loadconfig(std::string config_string);

private:
    // 后处理函数
    int postprocess(cv::Mat& img, float** output_tensor);

    // 分割后处理：将网络输出转换为分割掩码
    SegMask process_segmentation_output(float* output_data, int height, int width, int num_classes);

    // 将分割掩码上采样到原图尺寸
    SegMask resize_mask_to_original(const SegMask& mask, int orig_height, int orig_width);

private:
    // 模型输入参数
    int input_h = 1024;     // 模型输入高度
    int input_w = 2048;     // 模型输入宽度
    int input_c = 3;        // 模型输入通道数

    // 模型输出参数
    int output_h = 128;     // 模型输出高度
    int output_w = 256;     // 模型输出宽度
    int num_classes = 19;   // 类别数量

    // 后处理参数
    float confidence_threshold_ = 0.5;  // 置信度阈值

    // 调试参数
    int debug_show_result = 1;

    // 结果存储
    std::vector<SegMaskFlag> seg_results_;
    char* res_addr_;
    int max_data_size_;

    // 网络输出数据
    uint8_t* file_data = nullptr;
    unsigned int file_size = 0;
};

#endif // SLAM_SEG_H_
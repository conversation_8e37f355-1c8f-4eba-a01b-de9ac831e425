#include "slam_seg.h"
#include "utils/utils.h"
#include "shunzao_ai_task.h"
#include "shunzao_ai_lib.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>

extern int PRINT_LOG;

SlamSeg::SlamSeg(const char* model_path) {
    if (!setup(model_path)) {
        std::cerr << "[SlamSeg] Constructor failed to setup with model: " << model_path << std::endl;
    }

    // 计算最大数据大小：分割掩码大小 + 额外信息
    max_data_size_ = output_h * output_w + 1024;  // 预留额外空间
    res_addr_ = (char*)malloc(max_data_size_);
}

SlamSeg::~SlamSeg() {
    if(res_addr_) {
        free(res_addr_);
        res_addr_ = nullptr;
    }
}

bool SlamSeg::setup(const std::string& model_path) {
    std::cout << "[SlamSeg] Setting up with model: " << model_path << std::endl;
    // 使用 network_id = 0, 默认优先级 = 128
    nn_in_width_ = input_w;
    nn_in_height_ = input_h;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}

int SlamSeg::loadconfig(std::string config_string) {
    std::cout << "[SlamSeg] Loading config string: " << config_string << std::endl;
    rapidjson::Document document;
    document.Parse(config_string.data());

    if (document.HasParseError()) {
        std::cout << "[SlamSeg] Loading config file error" << std::endl;
        return -1;
    }

    // 解析配置参数
    if (document.HasMember("confidence_threshold") && document["confidence_threshold"].IsNumber()) {
        confidence_threshold_ = document["confidence_threshold"].GetFloat();
        std::cout << "[SlamSeg] confidence_threshold: " << confidence_threshold_ << std::endl;
    }

    if (document.HasMember("debug_show_result") && document["debug_show_result"].IsInt()) {
        debug_show_result = document["debug_show_result"].GetInt();
        std::cout << "[SlamSeg] debug_show_result: " << debug_show_result << std::endl;
    }

    return 0;
}

bool SlamSeg::run_inference(cv::Mat& img, InputParam* inp) {
    std::cout << "[SlamSeg] Running inference..." << std::endl;
    int ret = 0;
    file_data = this->preprocess(img, inp->coreid, &file_size);
    ret = load_input_set_output(file_data, file_size);

    if (!run_once()) {
        std::cerr << "[SlamSeg] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵
    if (!output_data) {
        std::cerr << "[SlamSeg] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(img, output_data) != 0) {
        std::cerr << "[SlamSeg] postprocess failed." << std::endl;
        return false;
    }

    // 释放存储模型output_data的内存
    delete_output_data(output_data);
    return true;
}

bool SlamSeg::run_inference_with_time(cv::Mat& img, InputParam* inp) {
    std::cout << "[SlamSeg] Running inference with time..." << std::endl;
    struct timeval start_time, end_time;

    gettimeofday(&start_time, nullptr);
    bool result = run_inference(img, inp);
    gettimeofday(&end_time, nullptr);

    long long total_time = (end_time.tv_sec - start_time.tv_sec) * 1000000 +
                          (end_time.tv_usec - start_time.tv_usec);
    std::cout << "[SlamSeg] Total inference time: " << total_time << " microseconds" << std::endl;

    return result;
}

bool SlamSeg::run_inference(cv::Mat& img, InputParam* inp, shunzaoAiTask* outer) {
    std::cout << "[SlamSeg] Running inference with timing..." << std::endl;
    int ret = 0;
    struct timeval run_start{}, run_end{};

    // 预处理计时
    gettimeofday(&run_start, nullptr);
    file_data = this->preprocess(img, inp->coreid, &file_size);
    ret = load_input_set_output(file_data, file_size);
    gettimeofday(&run_end, nullptr);
    long long preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                               (run_end.tv_usec - run_start.tv_usec);
    outer->setPreprocessTime(preprocess_time);

    // 推理计时
    gettimeofday(&run_start, nullptr);
    if (!run_once()) {
        std::cerr << "[SlamSeg] Network run failed." << std::endl;
        return false;
    }
    get_output_data(output_data);
    gettimeofday(&run_end, nullptr);
    long long inference_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                              (run_end.tv_usec - run_start.tv_usec);
    outer->setInferenceTime(inference_time);

    if (!output_data) {
        std::cerr << "[SlamSeg] Failed to get output." << std::endl;
        return false;
    }

    // 后处理计时
    gettimeofday(&run_start, nullptr);
    if (this->postprocess(img, output_data) != 0) {
        std::cerr << "[SlamSeg] postprocess failed." << std::endl;
        return false;
    }
    gettimeofday(&run_end, nullptr);
    long long postprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                                (run_end.tv_usec - run_start.tv_usec);
    outer->setPostprocessTime(postprocess_time);
    outer->setCount();

    // 释放存储模型output_data的内存
    delete_output_data(output_data);
    return true;
}

int SlamSeg::postprocess(cv::Mat& img, float** output_tensor) {
    std::cout << "[SlamSeg] postprocess..." << std::endl;

    seg_results_.clear(); // 清空结果

    // 模型输出: (1, 19, 128, 256)
    // output_tensor[0] 包含所有输出数据
    float* output_data = output_tensor[0];

    if (!output_data) {
        std::cerr << "[SlamSeg] Output data is null" << std::endl;
        return -1;
    }

    std::cout << "[SlamSeg] Processing segmentation output..." << std::endl;
    std::cout << "[SlamSeg] Original image size: " << img.cols << "x" << img.rows << std::endl;
    std::cout << "[SlamSeg] Network output size: " << output_w << "x" << output_h << std::endl;
    std::cout << "[SlamSeg] Number of classes: " << num_classes << std::endl;

    // 处理分割输出
    SegMask seg_mask = process_segmentation_output(output_data, output_h, output_w, num_classes);

    // 将分割掩码上采样到原图尺寸
    SegMask resized_mask = resize_mask_to_original(seg_mask, img.rows, img.cols);

    // 计算整体置信度（可以根据需要调整计算方法）
    float confidence = 0.8f;  // 简单设置，可以根据实际需求计算

    // 创建分割结果
    SegMaskFlag seg_result;
    seg_result.mask = resized_mask;
    seg_result.confidence = confidence;
    seg_result.flag = 1.0f;  // 标志位，表示有效结果

    seg_results_.push_back(seg_result);

    if (debug_show_result) {
        std::cout << "[SlamSeg] Segmentation result:" << std::endl;
        std::cout << "  - Mask size: " << resized_mask.width << "x" << resized_mask.height << std::endl;
        std::cout << "  - Number of classes: " << resized_mask.num_classes << std::endl;
        std::cout << "  - Confidence: " << confidence << std::endl;

        // 统计各类别像素数量
        std::vector<int> class_counts(num_classes, 0);
        for (int i = 0; i < resized_mask.mask_data.size(); i++) {
            if (resized_mask.mask_data[i] < num_classes) {
                class_counts[resized_mask.mask_data[i]]++;
            }
        }

        std::cout << "  - Class pixel counts:" << std::endl;
        for (int i = 0; i < num_classes; i++) {
            if (class_counts[i] > 0) {
                std::cout << "    Class " << i << ": " << class_counts[i] << " pixels" << std::endl;
            }
        }
    }

    return 0;
}

SegMask SlamSeg::process_segmentation_output(float* output_data, int height, int width, int num_classes) {
    std::cout << "[SlamSeg] Processing segmentation output..." << std::endl;

    SegMask mask(width, height, num_classes);

    // 输出格式: (1, 19, 128, 256) -> NCHW格式
    // 对于每个像素位置(h, w)，找到概率最大的类别
    for (int h = 0; h < height; h++) {
        for (int w = 0; w < width; w++) {
            int pixel_idx = h * width + w;
            float max_prob = -1.0f;
            int max_class = 0;

            // 遍历所有类别，找到概率最大的类别
            for (int c = 0; c < num_classes; c++) {
                // NCHW格式: output_data[c * height * width + h * width + w]
                int data_idx = c * height * width + h * width + w;
                float prob = output_data[data_idx];

                if (prob > max_prob) {
                    max_prob = prob;
                    max_class = c;
                }
            }

            // 存储预测的类别ID
            mask.mask_data[pixel_idx] = static_cast<uint8_t>(max_class);
        }
    }

    std::cout << "[SlamSeg] Segmentation processing completed" << std::endl;
    return mask;
}

SegMask SlamSeg::resize_mask_to_original(const SegMask& mask, int orig_height, int orig_width) {
    std::cout << "[SlamSeg] Resizing mask from " << mask.width << "x" << mask.height
              << " to " << orig_width << "x" << orig_height << std::endl;

    SegMask resized_mask(orig_width, orig_height, mask.num_classes);

    // 使用最近邻插值进行上采样
    float scale_x = static_cast<float>(mask.width) / orig_width;
    float scale_y = static_cast<float>(mask.height) / orig_height;

    for (int y = 0; y < orig_height; y++) {
        for (int x = 0; x < orig_width; x++) {
            // 计算在原始掩码中的对应位置
            int src_x = static_cast<int>(x * scale_x);
            int src_y = static_cast<int>(y * scale_y);

            // 边界检查
            src_x = std::min(src_x, mask.width - 1);
            src_y = std::min(src_y, mask.height - 1);

            int src_idx = src_y * mask.width + src_x;
            int dst_idx = y * orig_width + x;

            resized_mask.mask_data[dst_idx] = mask.mask_data[src_idx];
        }
    }

    std::cout << "[SlamSeg] Mask resizing completed" << std::endl;
    return resized_mask;
}

std::vector<SegMaskFlag> SlamSeg::get_prediction(int* size) {
    std::cout << "[SlamSeg] get_prediction..." << std::endl;

    if (size) {
        *size = seg_results_.size();
    }

    std::cout << "[SlamSeg] Returning " << seg_results_.size() << " segmentation results" << std::endl;
    return seg_results_;
}